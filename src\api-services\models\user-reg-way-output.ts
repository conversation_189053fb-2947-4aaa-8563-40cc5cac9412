/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * 注册方案输出参数
 *
 * @export
 * @interface UserRegWayOutput
 */
export interface UserRegWayOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof UserRegWayOutput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UserRegWayOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UserRegWayOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UserRegWayOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UserRegWayOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UserRegWayOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UserRegWayOutput
     */
    updateUserName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UserRegWayOutput
     */
    tenantId?: number | null;

    /**
     * 方案名称
     *
     * @type {string}
     * @memberof UserRegWayOutput
     */
    name?: string | null;

    /**
     * @type {AccountTypeEnum}
     * @memberof UserRegWayOutput
     */
    accountType?: AccountTypeEnum;

    /**
     * 注册用户默认角色
     *
     * @type {number}
     * @memberof UserRegWayOutput
     */
    roleId?: number;

    /**
     * 注册用户默认机构
     *
     * @type {number}
     * @memberof UserRegWayOutput
     */
    orgId?: number;

    /**
     * 注册用户默认职位
     *
     * @type {number}
     * @memberof UserRegWayOutput
     */
    posId?: number;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UserRegWayOutput
     */
    orderNo?: number;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UserRegWayOutput
     */
    remark?: string | null;

    /**
     * 角色名称
     *
     * @type {string}
     * @memberof UserRegWayOutput
     */
    roleName?: string | null;

    /**
     * 机构名称
     *
     * @type {string}
     * @memberof UserRegWayOutput
     */
    orgName?: string | null;

    /**
     * 职位名称
     *
     * @type {string}
     * @memberof UserRegWayOutput
     */
    posName?: string | null;
}
