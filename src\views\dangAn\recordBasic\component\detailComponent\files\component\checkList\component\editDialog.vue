﻿<script lang="ts" name="recordCheckList" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordCheckListApi } from '/@/api/dangAn/recordCheckList';
import { ExportRecordCatalogOutput } from '/@/api-services/dangAn/recordCatalog';

// 级联选择器配置选项
const cascaderProps = { checkStrictly: true, emitPath: false, value: 'id', label: 'name' };

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordCheckListApi = useRecordCheckListApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	recordId: null as Number|null,
	catalogData: [] as Array<ExportRecordCatalogOutput>, // 档案目录数据
});

// 自行添加其他规则
const rules = ref<FormRules>({
  recordId: [{required: true, message: '请选择档案号！', trigger: 'blur',},],
  catalogId: [{required: true, message: '请选择档案目录！', trigger: 'blur',},],
  fileName: [{required: true, message: '请选择材料名称！', trigger: 'blur',},],
  fileTime: [{required: true, message: '请选择形成时间！', trigger: 'change',},],
  fileUnit: [{required: true, message: '请选择单位！', trigger: 'blur',},],
  fileNumber: [{required: true, message: '请选择数量！', trigger: 'blur',},],
  fileIsCopyData: [{required: true, message: '请选择是否原件！', trigger: 'blur',},],
  fileIsSupplementData: [{required: true, message: '请选择是否补充！', trigger: 'blur',},],
  fileIsDigitization: [{required: true, message: '请选择数字化标识！', trigger: 'blur',},],
  weigh: [{required: true, message: '请选择权重！', trigger: 'blur',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string, recordId: Number, catalogData: Array<ExportRecordCatalogOutput>) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await recordCheckListApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.ruleForm.recordId = recordId;
	state.recordId = recordId;
	state.catalogData = catalogData;
	state.ruleForm.fileIsCopyData = false;
	state.ruleForm.fileIsSupplementData = false;
	state.ruleForm.fileIsDigitization = false;

	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			values.recordId = state.recordId;
			await recordCheckListApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

// 日期限制
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordCheckList-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案号" prop="recordId">
							<el-input v-model="state.ruleForm.recordId" placeholder="请输入档案号" maxlength="20" show-word-limit clearable disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="档案目录" prop="catalogId">
							<el-cascader :options="state.catalogData" :props="cascaderProps" placeholder="请选择档案目录" clearable filterable class="w100" v-model="state.ruleForm.catalogId">
								<template #default="{ node, data }">
									<span>{{ data.name }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-cascader>
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="材料名称" prop="fileName">
							<el-input v-model="state.ruleForm.fileName" placeholder="请输入材料名称" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="形成时间" prop="fileTime">
							<el-date-picker v-model="state.ruleForm.fileTime" type="date" placeholder="形成时间" :disabled-date="disabledDate" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="单位" prop="fileUnit">
							<el-select v-model="state.ruleForm.fileUnit" placeholder="请选择单位" style="width: 240px">
								<el-option label="份" value="份" />
								<el-option label="张" value="张" />
								<el-option label="页" value="页" />
							</el-select>
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="数量" prop="fileNumber">
							<el-input-number v-model="state.ruleForm.fileNumber" placeholder="请输入数量" :precision="0" :min="1" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="页数" prop="filePages">
							<el-input-number v-model="state.ruleForm.filePages" placeholder="请输入页数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="封面页数" prop="fileCoverPages">
							<el-input-number v-model="state.ruleForm.fileCoverPages" placeholder="请输入封面页数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否原件" prop="fileIsCopyData">
							<el-switch v-model="state.ruleForm.fileIsCopyData" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否补充" prop="fileIsSupplementData">
							<el-switch v-model="state.ruleForm.fileIsSupplementData" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="数字化标识" prop="fileIsDigitization">
							<el-switch v-model="state.ruleForm.fileIsDigitization" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="权重" prop="weigh">
							<el-input-number v-model="state.ruleForm.weigh" placeholder="请输入权重" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="备注" prop="fileContent">
							<el-input v-model="state.ruleForm.fileContent" type="textarea" :rows="2" placeholder="请输入备注" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>