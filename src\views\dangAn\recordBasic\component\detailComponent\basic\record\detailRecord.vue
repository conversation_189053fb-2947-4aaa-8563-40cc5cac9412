﻿<script lang="ts" setup name="recordRemark">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useRecordRemarkApi } from '/@/api/main/recordRemark';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import editDialog from './detailRecordEditDialog.vue';

const recordRemarkApi = useRecordRemarkApi();
const editDialogRef = ref();

//父级传递来的数据
const props = defineProps({
  recordId: { // 档案Id
    type: Number,
    required: true,
    default: null
  },
})

const state = reactive({
  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any, // 查询条件
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
});

// 页面加载时
onMounted(async () => {
  await handleQuery();
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;

  if(props.recordId != null){
    state.tableParams = Object.assign(state.tableParams, params);
    state.tableQueryParams.recordId = props.recordId;
    const result = await recordRemarkApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
    state.tableParams.total = result?.total;
    state.tableData = result?.items ?? [];
  }else {
    state.tableData = [];
  }

  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delRecordRemark = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await recordRemarkApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 批量删除
const batchDelRecordRemark = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await recordRemarkApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

handleQuery();
</script>
<template>
  <div class="recordRemark-container" v-loading="state.exportLoading">
    <el-card class="full-table" shadow="hover" style="margin-top: 15px">
      <el-button-group>
        <el-button type="primary"  icon="ele-RefreshRight" @click="handleQuery" v-auth="'recordBasic:page'" v-reclick="1000" />
        <el-button type="primary" style="margin-left:5px;" icon="ele-Plus" @click="editDialogRef.openDialog(null,'新增档案备注', props.recordId)" v-auth="'recordBasic:add'"> 新增 </el-button>
        <el-button type="danger" style="margin-left:5px;" icon="ele-Delete" @click="batchDelRecordRemark" :disabled="state.selectData.length == 0" v-auth="'recordBasic:batchDelete'"> 批量删除 </el-button>
      </el-button-group>

      <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }" style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id" @sort-change="sortChange" border>
        <el-table-column type="selection" width="40" align="center" v-if="auth('recordBasic:batchDelete') || auth('recordBasic:export')" />
        <el-table-column type="index" label="序号" width="55" align="center"/>
        <el-table-column prop='createTime' label='时间' show-overflow-tooltip />
        <el-table-column prop='content' label='备注' show-overflow-tooltip />
        <el-table-column prop='tips' label='提示' show-overflow-tooltip />
        <el-table-column prop='adminName' label='录入人' show-overflow-tooltip />
        <el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            <ModifyRecord :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('recordBasic:update') || auth('recordBasic:delete')">
          <template #default="scope">
            <el-button icon="ele-Delete" size="small" text type="primary" @click="delRecordRemark(scope.row)" v-auth="'recordBasic:delete'"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination 
              v-model:currentPage="state.tableParams.page"
              v-model:page-size="state.tableParams.pageSize"
              @size-change="(val: any) => handleQuery({ pageSize: val })"
              @current-change="(val: any) => handleQuery({ page: val })"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100, 200, 500]"
              :total="state.tableParams.total"
              size="small"
              background />
      <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>
<style scoped>
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>