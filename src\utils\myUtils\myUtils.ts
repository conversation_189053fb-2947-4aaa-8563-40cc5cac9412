import { GenderEnum } from  '/@/api-services/models/gender-enum'

// 从身份证号码字符串中获取日期
export const getBirthDateFromIdCard = (idCard: string) => {
    // 检查身份证号码有效性（简单验证长度）
    if (idCard.length !== 15 && idCard.length !== 18) {
        throw new Error('请输入有效的15位或18位身份证号码');
    }
    
    let year, month, day;
    
    if (idCard.length === 15) {
        // 15位身份证：第7-12位为出生日期，格式为YYMMDD
        year = '19' + idCard.substring(6, 8);
        month = idCard.substring(8, 10);
        day = idCard.substring(10, 12);
    } else {
        // 18位身份证：第7-14位为出生日期，格式为YYYYMMDD
        year = idCard.substring(6, 10);
        month = idCard.substring(10, 12);
        day = idCard.substring(12, 14);
    }
    
    // 转换为数字
    year = parseInt(year, 10);
    month = parseInt(month, 10) - 1; // 月份在Date中是0-11
    day = parseInt(day, 10);
    
    // 创建Date对象
    const birthDate = new Date(year, month, day);
    
    // 验证日期有效性
    if (
        birthDate.getFullYear() !== year ||
        birthDate.getMonth() !== month ||
        birthDate.getDate() !== day
    ) {
        throw new Error('身份证号码中的出生日期无效');
    }
    
    return birthDate;
}

// 从身份证号码字符串中获取性别
export const getGenderFromIdCard = (idCard:string) : GenderEnum => {
    
    // 检查身份证长度是否合法
    if (idCard.length !== 15 && idCard.length !== 18) {
        return GenderEnum.NUMBER_0;
    }
    
    let genderCode;
    try {
        if (idCard.length === 15) {
            // 15位身份证：第15位为性别码（奇数为男，偶数为女）
            genderCode = parseInt(idCard.charAt(14), 10);
        } else {
            // 18位身份证：第17位为性别码（奇数为男，偶数为女）
            genderCode = parseInt(idCard.charAt(16), 10);
        }
        
        // 判断性别（奇数为男，偶数为女）
        return genderCode % 2 === 1 ? GenderEnum.NUMBER_1 : GenderEnum.NUMBER_2;
    } catch (error) {
        // 解析过程出错时返回0
        return GenderEnum.NUMBER_0;
    }
}

// 校验身份证是否正确
export const validateIdCard = (idCard:string) : boolean =>  {
    // 1. 检查基本格式
    if (typeof idCard !== 'string' || idCard.length !== 18) {
        return false;
    }
    
    // 2. 检查前17位是否为数字，第18位可以是数字或X/x
    const reg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
    if (!reg.test(idCard)) {
        return false;
    }
    
    // 3. 验证出生日期
    const year = parseInt(idCard.substring(6, 10), 10);
    const month = parseInt(idCard.substring(10, 12), 10) - 1; // 月份从0开始
    const day = parseInt(idCard.substring(12, 14), 10);
    const birthDate = new Date(year, month, day);
    
    if (
        birthDate.getFullYear() !== year ||
        birthDate.getMonth() !== month ||
        birthDate.getDate() !== day
    ) {
        return false;
    }
    
    // 4. 验证校验码
    const factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const parityCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    let sum = 0;
    
    for (let i = 0; i < 17; i++) {
        sum += parseInt(idCard[i], 10) * factors[i];
    }
    
    const checkCode = parityCodes[sum % 11];
    return checkCode.toUpperCase() === idCard[17].toUpperCase();
}