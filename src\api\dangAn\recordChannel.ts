﻿import {useBaseApi} from '/@/api/base';

// 档案柜管理接口服务
export const useRecordChannelApi = () => {
	const baseApi = useBaseApi("recordChannel");
	return {
		// 分页查询档案柜管理
		page: baseApi.page,
		// 查看档案柜管理详细
		detail: baseApi.detail,
		// 新增档案柜管理
		add: baseApi.add,
		// 更新档案柜管理
		update: baseApi.update,
		// 删除档案柜管理
		delete: baseApi.delete,
		// 批量删除档案柜管理
		batchDelete: baseApi.batchDelete,
		// 导出档案柜管理数据
		exportData: baseApi.exportData,
		// 导入档案柜管理数据
		importData: baseApi.importData,
		// 下载档案柜管理数据导入模板
		downloadTemplate: baseApi.downloadTemplate,


		// 树状查询档案柜
		query: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "query",
                method: 'post',
                data,
            }, cancel);
        },

		// 树状查询启用的档案柜
		queryEnable: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "queryEnable",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 档案柜管理实体
export interface RecordChannel {
	// 主键Id
	id: number;
	// 模型Id
	modelId: number;
	// 父Id
	parentId: number;
	// 名称
	name: string;
	// 图片
	image: string;
	// 标志
	flag: string;
	// SEO标题
	seotitle: string;
	// 关键字
	keyWords: string;
	// 描述
	description: string;
	// 自定义名称
	diyName: string;
	// 外部链接
	outLink: string;
	// 链接类型
	linkType: string;
	// 实际档案数量
	items: number;
	// 列表数据类型
	listType: number;
	// 是否已满 true:已满 false:未满
	isContribute?: boolean;
	// 是否导航显示 true:是 false:否
	isNav: boolean;
	// 状态
	status?: number;
	// 库房Id
	cabinetRecordRoomId: number;
	// 可存放档案数量
	cabinetMax: number;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}