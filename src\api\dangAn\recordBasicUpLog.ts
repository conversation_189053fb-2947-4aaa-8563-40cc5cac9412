﻿import {useBaseApi} from '/@/api/base';

// 档案修改记录接口服务
export const useRecordBasicUpLogApi = () => {
	const baseApi = useBaseApi("recordBasicUpLog");
	return {
		// 分页查询档案修改记录
		page: baseApi.page,
		// 查看档案修改记录详细
		detail: baseApi.detail,
		// 新增档案修改记录
		add: baseApi.add,
		// 更新档案修改记录
		update: baseApi.update,
		// 删除档案修改记录
		delete: baseApi.delete,
		// 批量删除档案修改记录
		batchDelete: baseApi.batchDelete,
		// 导出档案修改记录数据
		exportData: baseApi.exportData,
		// 导入档案修改记录数据
		importData: baseApi.importData,
		// 下载档案修改记录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}