﻿<script lang="ts" setup name="recordEntryandout">
import { ref, reactive, onMounted, watch } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordEntryandoutApi } from '/@/api/dangAn/recordEntryandout';
import entryandoutEditDialog from './entryandoutEditDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from '/@/components/table/importData.vue';
import { QueryRecordChanneOutput } from '/@/api-services/dangAn/recordChannel';
import treePath from '/@/components/treePath/index.vue';

//父级传递来的数据
const props = defineProps({
	recordId: {
		// 档案Id
		type: Number,
		required: true,
		default: null,
	},
	channelData: {
		// 档案柜数据
		type: Array<QueryRecordChanneOutput>,
		required: true,
		default: [],
	},
});

const recordEntryandoutApi = useRecordEntryandoutApi();
const printDialogRef = ref();
const entryandoutEditDialogRef = ref();
const importDataRef = ref();
const state = reactive({
	exportLoading: false,
	tableLoading: false,
	stores: {},
	showAdvanceQueryUI: false,
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'createTime', // 默认的排序字段
		order: 'descending', // 排序方向
		descStr: 'descending', // 降序排序的关键字符
	},
	tableData: [],
	newData: {} as any, // 最新记录
});

// 页面加载时
onMounted(async () => {
	// 如果 channelData 已经有数据，直接查询
	if (props.channelData && props.channelData.length > 0) {
		handleQuery();
	}
});

// 监听 channelData 变化，当父组件数据加载完成后重新查询
watch(
	() => props.channelData,
	(newChannelData) => {
		// 当 channelData 从空变为有数据时，重新查询
		if (newChannelData && newChannelData.length > 0) {
			handleQuery();
		}
	},
	{ immediate: false, deep: true }
);

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;

	// 历史记录
	state.tableParams = Object.assign(state.tableParams, params);
	state.tableQueryParams.recordId = props.recordId;
	const result = await recordEntryandoutApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
	state.tableParams.total = result?.total;
	state.tableData = result?.items ?? [];

	// 最新记录
	state.newData = (await recordEntryandoutApi.first({ recordId: props.recordId }).then((res) => res.data.result)) ?? {};

	state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};
</script>
<template>
	<div class="recordEntryandout-container" v-loading="state.exportLoading">
		<el-card>
			<template #header>
				<div class="card-header">
					<span>当前档案状态</span>
				</div>
			</template>

			<el-descriptions :column="4" border size="small" label-width="120px">
				<el-descriptions-item label="档案号">{{ props.recordId }}</el-descriptions-item>
				<el-descriptions-item label="出入库类型"><g-sys-dict v-model="state.newData.logType" code="RecordLogTypeEnum" /></el-descriptions-item>
				<el-descriptions-item label="所在档案柜">
					<treePath :data="props.channelData" children="children" label="name" hasChildren="parentId" :value="+state.newData.channelId" />
				</el-descriptions-item>
				<el-descriptions-item label="修改者姓名">{{ state.newData.recordUpdateUserName }}</el-descriptions-item>
				<el-descriptions-item label="出入库备注">{{ state.newData.mark }}</el-descriptions-item>
			</el-descriptions>

			<div style="margin-top: 10px">
				<el-button
					type="primary"
					style="margin-left: 5px"
					icon="ele-Plus"
					@click="entryandoutEditDialogRef.openDialog('新增档案出库入库记录', props.recordId, props.channelData)"
					v-auth="'recordEntryandout:add'"
				>
					新增档案出库入库记录
				</el-button>
			</div>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<template #header>
				<div class="card-header">
					<span>历史记录</span>
				</div>
			</template>

			<!-- 查询 -->
			<el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="出入库类型">
							<g-sys-dict v-model="state.tableQueryParams.logType" code="RecordLogTypeEnum" render-as="select" placeholder="请选择出入库类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="修改者姓名">
							<el-input v-model="state.tableQueryParams.recordUpdateUserName" clearable placeholder="请输入修改者姓名" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="出入库备注">
							<el-input v-model="state.tableQueryParams.mark" clearable placeholder="请输入出入库备注" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordEntryandout:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" v-if="!state.showAdvanceQueryUI" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" v-if="state.showAdvanceQueryUI" style="margin-left: 5px"> 隐藏 </el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<!-- 查询结果列表 -->
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" v-if="auth('recordEntryandout:batchDelete') || auth('recordEntryandout:export')" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="createTime" label="时间" show-overflow-tooltip />
				<el-table-column prop="logType" label="出入库类型" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.logType" code="RecordLogTypeEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="logType" label="出入库类型" show-overflow-tooltip>
					<template #default="scope">
						<treePath :data="props.channelData" children="children" label="name" hasChildren="parentId" :value="+scope.row.channelId" />
					</template>
				</el-table-column>
				<el-table-column prop="recordUpdateUserName" label="修改者姓名" show-overflow-tooltip />
				<el-table-column prop="mark" label="出入库备注" show-overflow-tooltip />
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
			<ImportData ref="importDataRef" :import="recordEntryandoutApi.importData" :download="recordEntryandoutApi.downloadTemplate" v-auth="'recordEntryandout:import'" @refresh="handleQuery" />
			<printDialog ref="printDialogRef" :title="'打印档案出库入库记录'" @reloadTable="handleQuery" />
			<entryandoutEditDialog ref="entryandoutEditDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
