<script lang="ts" setup name="detailYue">
import { ref, reactive, onMounted } from "vue";
import { useRoute, useRouter } from 'vue-router';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { QueryRecordChanneOutput } from '/@/api-services/dangAn/recordChannel';
import consultDialog from './consult/index.vue';
import borrowDialog from './borrow/index.vue';


//父级传递来的数据
const props = defineProps({
  recordId: { // 档案Id
    type: Number,
    required: true,
    default: null
  },
})

const state = reactive({
  exportLoading: false,
});

// 页面加载时
onMounted(async () => {
});



</script>
<template>
  <div class="detailYue-container" v-loading="state.exportLoading">

    <el-tabs type="border-card">

        <el-tab-pane label="档案查阅">
            <consultDialog :recordId="props.recordId" />
        </el-tab-pane>

        <el-tab-pane label="档案借阅">
            <borrowDialog :recordId="props.recordId" />
        </el-tab-pane>

    </el-tabs>

    
  </div>
</template>
<style scoped>
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>