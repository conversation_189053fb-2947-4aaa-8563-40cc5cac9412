﻿<script lang="ts" name="recordChannel" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordChannelApi } from '/@/api/dangAn/recordChannel';
import { QueryRecordChanneOutput } from '/@/api-services/dangAn/recordChannel';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordChannelApi = useRecordChannelApi();
const ruleFormRef = ref();

// 级联选择器配置选项
const cascaderProps = { checkStrictly: true, emitPath: false, value: 'id', label: 'name' };

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	menuData: [] as Array<QueryRecordChanneOutput>
});

// 自行添加其他规则
const rules = ref<FormRules>({
  isContribute: [{required: true, message: '请选择是否已满 true:已满 false:未满！', trigger: 'blur',},],
  status: [{required: true, message: '请选择状态！', trigger: 'change',},],
  listType: [{required: true, message: '请选择列表数据类型！', trigger: 'change',},],
  name: [{required: true, message: '请输入名称！', trigger: 'change',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await recordChannelApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.menuData = await recordChannelApi.query({}).then(res => res.data.result) ?? [];
	if(state.ruleForm?.isContribute == null) state.ruleForm.isContribute = false;

	// 默认选项
	if(state.ruleForm?.status == null)state.ruleForm.status = 1;
	if(state.ruleForm?.listType == null)state.ruleForm.listType = 0;
	if(state.ruleForm?.isContribute == null)state.ruleForm.isContribute = true;

	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await recordChannelApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordChannel-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="上级菜单">
							<el-cascader :options="state.menuData" :props="cascaderProps" placeholder="请选择上级菜单" clearable filterable class="w100" v-model="state.ruleForm.parentId">
								<template #default="{ node, data }">
									<span>{{ data.name }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-cascader>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="名称" prop="name">
							<el-input v-model="state.ruleForm.name" placeholder="请输入名称" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="自定义名称" prop="diyName">
							<el-input v-model="state.ruleForm.diyName" placeholder="请输入自定义名称" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="图片" prop="image">
							<el-input v-model="state.ruleForm.image" placeholder="请输入图片" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="标志" prop="flag">
							<el-input v-model="state.ruleForm.flag" placeholder="请输入标志" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="SEO标题" prop="seotitle">
							<el-input v-model="state.ruleForm.seotitle" placeholder="请输入SEO标题" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="关键字" prop="keyWords">
							<el-input v-model="state.ruleForm.keyWords" placeholder="请输入关键字" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="描述" prop="description">
							<el-input v-model="state.ruleForm.description" placeholder="请输入描述" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="外部链接" prop="outLink">
							<el-input v-model="state.ruleForm.outLink" placeholder="请输入外部链接" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="链接类型" prop="linkType">
							<g-sys-dict v-model="state.ruleForm.linkType" code="Link_type" render-as="select" placeholder="请选择链接类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="列表数据类型" prop="listType">
							<g-sys-dict v-model="state.ruleForm.listType" code="RecordChannelListTypeEnum" render-as="select" placeholder="请选择列表数据类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="可存放档案数量" prop="cabinetMax">
							<el-input-number v-model="state.ruleForm.cabinetMax" placeholder="请输入可存放档案数量" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否已满" prop="isContribute">
							<el-switch v-model="state.ruleForm.isContribute" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否导航显示" prop="isNav">
							<el-switch v-model="state.ruleForm.isNav" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="状态" prop="status">
							<g-sys-dict v-model="state.ruleForm.status" code="StatusEnum" render-as="select" placeholder="请选择状态" clearable filterable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>