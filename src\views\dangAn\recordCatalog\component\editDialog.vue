﻿<script lang="ts" name="recordCatalog" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordCatalogApi } from '/@/api/dangAn/recordCatalog';
import { ExportRecordCatalogOutput } from '/@/api-services/dangAn/recordCatalog';

// 级联选择器配置选项
const cascaderProps = { checkStrictly: true, emitPath: false, value: 'id', label: 'name' };

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordCatalogApi = useRecordCatalogApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	menuData: [] as Array<ExportRecordCatalogOutput>, // 档案桂数据
});

// 自行添加其他规则
const rules = ref<FormRules>({
  name: [{required: true, message: '请输入名称！', trigger: 'blur',},],
  weigh: [{required: true, message: '请输入权重！', trigger: 'blur',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await recordCatalogApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));

	state.menuData = await recordCatalogApi.query({}).then(res => res.data.result) ?? [];

	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await recordCatalogApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordCatalog-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="上级" prop="fatherId">
							<el-cascader :options="state.menuData" :props="cascaderProps" placeholder="请选择上级" clearable filterable class="w100" v-model="state.ruleForm.fatherId">
								<template #default="{ node, data }">
									<span>{{ data.name }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-cascader>
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="名称" prop="name">
							<el-input v-model="state.ruleForm.name" placeholder="请输入名称" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="权重" prop="weigh">
							<el-input-number v-model="state.ruleForm.weigh" placeholder="请输入权重" :precision="0" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="描述" prop="description">
							<el-input v-model="state.ruleForm.description" type="textarea" :rows="2" placeholder="请输入描述" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>