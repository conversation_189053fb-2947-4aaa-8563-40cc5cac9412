<script lang="ts" setup name="recordBasic">
import { ref, reactive, onMounted } from "vue";
import { useRoute, useRouter } from 'vue-router';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import checkListDialog from './component/checkList/index.vue';
import { ExportRecordCatalogOutput } from '/@/api-services/dangAn/recordCatalog';


//父级传递来的数据
const props = defineProps({
  recordId: { // 档案Id
    type: Number,
    required: true,
    default: null
  },
  catalogData: {  // 档案目录数据
    type: Array<ExportRecordCatalogOutput>,
    required: true,
    default: []
  }
})

const state = reactive({
  exportLoading: false,
});

// 页面加载时
onMounted(async () => {
});



</script>
<template>
  <div class="recordBasic-container" v-loading="state.exportLoading">
    <checkListDialog :recordId="props.recordId" :catalogData="props.catalogData" />
  </div>
</template>
<style scoped>
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>