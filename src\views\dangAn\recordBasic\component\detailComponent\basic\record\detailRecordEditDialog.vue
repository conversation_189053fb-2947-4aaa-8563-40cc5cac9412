﻿<script lang="ts" name="recordRemark" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordRemarkApi } from '/@/api/main/recordRemark';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordRemarkApi = useRecordRemarkApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	detailId: null as Number | null, // 档案id
});

// 自行添加其他规则
const rules = ref<FormRules>({
  recordId: [{required: true, message: '请选择档案号！', trigger: 'blur',},],
  adminId: [{required: true, message: '请选择录入人Id！', trigger: 'blur',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string, detailId: Number) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await recordRemarkApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));

	state.detailId = detailId;
	state.ruleForm.recordId = detailId;

	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			state.ruleForm.recordId = state.detailId;
			let values = state.ruleForm;
			await recordRemarkApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordRemark-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案号" prop="recordId">
							<el-input v-model="state.ruleForm.recordId" placeholder="请输入档案号" maxlength="20" show-word-limit clearable disabled />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="备注" prop="content">
							<el-input v-model="state.ruleForm.content" placeholder="请输入备注" maxlength="255" show-word-limit clearable type="textarea" :autosize="{ minRows: 2 }" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="提示" prop="tips">
							<el-input v-model="state.ruleForm.tips" placeholder="请输入提示" maxlength="255" show-word-limit clearable type="textarea" :autosize="{ minRows: 2 }" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>