/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DbType } from './db-type';
import { StatusEnum } from './status-enum';
import { TenantTypeEnum } from './tenant-type-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 
 *
 * @export
 * @interface UpdateTenantInput
 */
export interface UpdateTenantInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof UpdateTenantInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateTenantInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateTenantInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateTenantInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateTenantInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    updateUserName?: string | null;

    /**
     * 租管用户Id
     *
     * @type {number}
     * @memberof UpdateTenantInput
     */
    userId?: number;

    /**
     * 机构Id
     *
     * @type {number}
     * @memberof UpdateTenantInput
     */
    orgId?: number;

    /**
     * 域名
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    host?: string | null;

    /**
     * @type {TenantTypeEnum}
     * @memberof UpdateTenantInput
     */
    tenantType?: TenantTypeEnum;

    /**
     * @type {DbType}
     * @memberof UpdateTenantInput
     */
    dbType?: DbType;

    /**
     * 数据库连接
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    connection?: string | null;

    /**
     * 数据库标识
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    configId?: string | null;

    /**
     * 从库连接/读写分离
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    slaveConnections?: string | null;

    /**
     * @type {YesNoEnum}
     * @memberof UpdateTenantInput
     */
    enableReg?: YesNoEnum;

    /**
     * 默认注册方案Id
     *
     * @type {number}
     * @memberof UpdateTenantInput
     */
    regWayId?: number | null;

    /**
     * 图标
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    logo?: string | null;

    /**
     * 水印
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    watermark?: string | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateTenantInput
     */
    orderNo?: number;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateTenantInput
     */
    status?: StatusEnum;

    /**
     * 电子邮箱
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    email?: string | null;

    /**
     * 电话
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    phone?: string | null;

    /**
     * 租户名称
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    name: string;

    /**
     * 租管账号
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    adminAccount: string;

    /**
     * 系统主标题
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    title?: string | null;

    /**
     * 系统副标题
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    viceTitle?: string | null;

    /**
     * 系统描述
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    viceDesc?: string | null;

    /**
     * 版权说明
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    copyright?: string | null;

    /**
     * ICP备案号
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    icp?: string | null;

    /**
     * ICP地址
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    icpUrl?: string | null;

    /**
     * Logo图片Base64码
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    logoBase64?: string | null;

    /**
     * Logo文件名
     *
     * @type {string}
     * @memberof UpdateTenantInput
     */
    logoFileName?: string | null;
}
