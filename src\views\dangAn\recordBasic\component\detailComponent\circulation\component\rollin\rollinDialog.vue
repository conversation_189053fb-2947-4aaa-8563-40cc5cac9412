<script lang="ts" setup name="recordBasic">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordRollinApi } from '/@/api/dangAn/recordRollin';
import rollinEditDialog from './rollinEditDialog.vue';

//父级传递来的数据
const props = defineProps({
	recordId: {
		// 档案Id
		type: Number,
		required: true,
		default: null,
	},
});

const recordRollinApi = useRecordRollinApi();
const editDialogRef = ref();
const state = reactive({
	tableLoading: false,

	rollinData: {} as any, // 转入信息
});

// 页面加载时
onMounted(async () => {
	handleQuery();
});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;

	if (props.recordId != null) {
		let list = (await recordRollinApi.queryRecordId({ recordId: props.recordId }).then((res) => res.data.result)) ?? [];
		if (list != null && list.length > 0) state.rollinData = list[0];
		else state.rollinData = {};
	} else {
		state.rollinData = {};
	}

	state.tableLoading = false;
};

//将属性或者函数暴露给父组件
defineExpose({ handleQuery });
</script>
<template>
	<div>
		<el-descriptions :column="4" border size="small" label-width="120px">
			<el-descriptions-item label="档案号">{{ state.rollinData.recordId }}</el-descriptions-item>
			<el-descriptions-item label="原档案所在地">{{ state.rollinData.oldOrganization }}</el-descriptions-item>
			<el-descriptions-item label="原档案所在机构地址">{{ state.rollinData.oldOrganizationAddress }}</el-descriptions-item>
			<el-descriptions-item label="原档案所在机构联系方式">{{ state.rollinData.oldOrganizationTel }}</el-descriptions-item>
			<el-descriptions-item label="档案到达时间">{{ state.rollinData.recordArrivalTime }}</el-descriptions-item>
			<el-descriptions-item label="转入机构名称">{{ state.rollinData.inOrganization }}</el-descriptions-item>
			<el-descriptions-item label="转入原因"><g-sys-dict v-model="state.rollinData.inReason" code="Circulation_reason" /></el-descriptions-item>
			<el-descriptions-item label="个人身份"><g-sys-dict v-model="state.rollinData.personageType" code="User_type" /></el-descriptions-item>
			<el-descriptions-item label="原企业性质"><g-sys-dict v-model="state.rollinData.oldCompanyNature" code="Company_nature" /></el-descriptions-item>
			<el-descriptions-item label="转入方式"><g-sys-dict v-model="state.rollinData.inType" code="Circulation_type" /></el-descriptions-item>
			<el-descriptions-item label="签订保管协议时间">{{ state.rollinData.identificationTime }}</el-descriptions-item>
			<el-descriptions-item label="转入机要号">{{ state.rollinData.inConfidentialNumber }}</el-descriptions-item>
			<el-descriptions-item label="办理人事代理时间">{{ state.rollinData.humanAgencyTime }}</el-descriptions-item>
			<el-descriptions-item label="人事代理办理人">{{ state.rollinData.humanAgencyPerson }}</el-descriptions-item>
			<el-descriptions-item label="回执情况"><g-sys-dict v-model="state.rollinData.receiptCondition" code="Receipt_condition" /></el-descriptions-item>
			<el-descriptions-item label="因执寄出日期">{{ state.rollinData.outReceiptTime }}</el-descriptions-item>
			<el-descriptions-item label="回执寄往单位">{{ state.rollinData.outReceiptCompany }}</el-descriptions-item>
			<el-descriptions-item label="回执寄往地址">{{ state.rollinData.outReceiptAddress }}</el-descriptions-item>
			<el-descriptions-item label="回执接收人">{{ state.rollinData.receiptPerson }}</el-descriptions-item>
			<el-descriptions-item label="回执接收电话">{{ state.rollinData.receiptTel }}</el-descriptions-item>
			<el-descriptions-item label="邮编">{{ state.rollinData.receiptPostalCode }}</el-descriptions-item>
			<el-descriptions-item label="接收人员">{{ state.rollinData.personIn }}</el-descriptions-item>
		</el-descriptions>

		<el-button icon="ele-Edit" style="margin-top: 10px" size="small" type="primary" @click="editDialogRef.openDialog(state.rollinData, '编辑转入信息', props.recordId)" v-auth="'recordRollin:update'">
			编辑
		</el-button>

		<rollinEditDialog ref="editDialogRef" @reloadTable="handleQuery(state.rollinData)" />
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}

.linkedList :deep(.el-radio-button__inner) {
	width: 100%;
}
</style>
