﻿<script lang="ts" name="recordRollout" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordRolloutApi } from '/@/api/dangAn/recordRollout';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordRolloutApi = useRecordRolloutApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,

	recordId: null as Number | null, // 详细id
});

// 自行添加其他规则
const rules = ref<FormRules>({
  recordId: [{required: true, message: '档案号不能为空！', trigger: 'blur',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string, recordId: Number) => {
	state.title = title;
	row = row ?? {  };
	
	if(row.recordId != null){
	  state.recordId = row.recordId;
      let list =  await recordRolloutApi.queryRecordId({recordId: row.recordId}).then(res => res.data.result) ?? [];
      if(list != null && list.length > 0) state.ruleForm = list[0];
      else state.ruleForm = JSON.parse(JSON.stringify(row));
    }else {
      state.ruleForm = JSON.parse(JSON.stringify(row));
	  state.ruleForm.recordId = recordId;
	  state.recordId = recordId;

	  if(recordId == null){
		ElMessage({
			message: `档案号不能为空！`,
			type: "error",
		});
		state.showDialog = false;
		return;
	  }
    }

	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;

			values.recordId = state.recordId;
			
			await recordRolloutApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordRollout-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案号" prop="recordId">
							<el-input v-model="state.ruleForm.recordId" placeholder="请输入档案号" maxlength="20" show-word-limit clearable disabled />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转出申请时间" prop="applyTime">
							<el-date-picker v-model="state.ruleForm.applyTime" type="date" placeholder="转出申请时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转出状态" prop="status">
							<el-input v-model="state.ruleForm.status" placeholder="请输入转出状态" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转出原因" prop="reason">
							<g-sys-dict v-model="state.ruleForm.reason" code="Circulation_reason" render-as="select" placeholder="请选择转出原因" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转至机构名称" prop="organization">
							<el-input v-model="state.ruleForm.organization" placeholder="请输入转至机构名称" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="单位性质" prop="companyNature">
							<g-sys-dict v-model="state.ruleForm.companyNature" code="Company_nature" render-as="select" placeholder="请选择单位性质" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转出地址" prop="address">
							<el-input v-model="state.ruleForm.address" placeholder="请输入转出地址" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="邮编" prop="postalCode">
							<el-input-number v-model="state.ruleForm.postalCode" placeholder="请输入邮编" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案接收人" prop="recipient">
							<el-input v-model="state.ruleForm.recipient" placeholder="请输入档案接收人" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="电话" prop="recipientTel">
							<el-input v-model="state.ruleForm.recipientTel" placeholder="请输入电话" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转出提醒时间" prop="tipTime">
							<el-date-picker v-model="state.ruleForm.tipTime" type="date" placeholder="转出提醒时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转出表编号" prop="serialNumber">
							<el-input v-model="state.ruleForm.serialNumber" placeholder="请输入转出表编号" maxlength="20" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转出机要号" prop="confidentialNumber">
							<el-input v-model="state.ruleForm.confidentialNumber" placeholder="请输入转出机要号" maxlength="30" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案转出时间" prop="outTime">
							<el-date-picker v-model="state.ruleForm.outTime" type="date" placeholder="档案转出时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="人事关系转出" prop="humanAgencey">
							<el-input v-model="state.ruleForm.humanAgencey" placeholder="请输入人事关系转出" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="人事关系转出时间" prop="humanAgenceyTime">
							<el-date-picker v-model="state.ruleForm.humanAgenceyTime" type="date" placeholder="人事关系转出时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="行政介绍信" prop="introductionLetter">
							<el-input v-model="state.ruleForm.introductionLetter" placeholder="请输入行政介绍信" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="行政介绍信出具人" prop="introductionLetterPerson">
							<el-input v-model="state.ruleForm.introductionLetterPerson" placeholder="请输入行政介绍信出具人" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="行政介绍信开出日期" prop="introductionLetterAddtime">
							<el-date-picker v-model="state.ruleForm.introductionLetterAddtime" type="date" placeholder="行政介绍信开出日期" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="工资介绍信" prop="payrollLetter">
							<el-input v-model="state.ruleForm.payrollLetter" placeholder="请输入工资介绍信" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="工资介绍信出具人" prop="payrollLetterPerson">
							<el-input v-model="state.ruleForm.payrollLetterPerson" placeholder="请输入工资介绍信出具人" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转出人员" prop="personOut">
							<el-input v-model="state.ruleForm.personOut" placeholder="请输入转出人员" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>