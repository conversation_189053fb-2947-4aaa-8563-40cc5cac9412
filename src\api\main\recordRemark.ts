﻿import {useBaseApi} from '/@/api/base';

// 档案备注接口服务
export const useRecordRemarkApi = () => {
	const baseApi = useBaseApi("recordRemark");
	return {
		// 分页查询档案备注
		page: baseApi.page,
		// 查看档案备注详细
		detail: baseApi.detail,
		// 新增档案备注
		add: baseApi.add,
		// 更新档案备注
		update: baseApi.update,
		// 删除档案备注
		delete: baseApi.delete,
		// 批量删除档案备注
		batchDelete: baseApi.batchDelete,
		// 导出档案备注数据
		exportData: baseApi.exportData,
		// 导入档案备注数据
		importData: baseApi.importData,
		// 下载档案备注数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 档案备注实体
export interface RecordRemark {
	// 主键Id
	id: number;
	// 档案号
	recordId?: number;
	// 备注
	content: string;
	// 提示
	tips: string;
	// 录入人Id
	adminId?: number;
	// 录入人
	adminName: string;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}