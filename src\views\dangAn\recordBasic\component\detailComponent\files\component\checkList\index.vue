﻿<script lang="ts" setup name="recordCheckList">
import { ref, reactive, onMounted, watch } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordCheckListApi } from '/@/api/dangAn/recordCheckList';
import editDialog from './component/editDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from '/@/components/table/importData.vue';
import { ExportRecordCatalogOutput } from '/@/api-services/dangAn/recordCatalog';
import { QueryRecordCheckListOutput } from '/@/api-services/dangAn/recordCheckList';
import { RefordFileEnum } from '/@/api-services/dangAn/refordFileEnum';
import treePath from '/@/components/treePath/index.vue';

//父级传递来的数据
const props = defineProps({
	recordId: {
		// 档案Id
		type: Number,
		required: true,
		default: null,
	},
	catalogData: {
		// 档案目录数据
		type: Array<ExportRecordCatalogOutput>,
		required: true,
		default: [],
	},
});

const recordCheckListApi = useRecordCheckListApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
	tableLoading: false, // 加载
	fileData: [], // 材料数据
	enemData: [] as Array<QueryRecordCheckListOutput>, // 树状数据
});

// 监听 catalogData 变化，当父组件数据加载完成后重新查询
watch(
	() => props.catalogData,
	(newCatalogData) => {
		// 当 catalogData 从空变为有数据时，重新查询
		if (newCatalogData && newCatalogData.length > 0) {
			handleQuery();
		}
	},
	{ immediate: false, deep: true }
);

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.fileData = (await recordCheckListApi.query({ recordId: props.recordId }).then((res) => res.data.result)) ?? [];

	state.enemData = fusion(state.fileData, props.catalogData);

	state.tableLoading = false;
};

// 删除
const delRecordCheckList = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordCheckListApi.delete({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 生成树状数据
const fusion = (fileData: Array<any>, catalogData: Array<ExportRecordCatalogOutput>): Array<QueryRecordCheckListOutput> => {
	// 创建材料到档案目录的映射
	const fileMap = new Map<number, Array<any>>();
	fileData.forEach((item) => {
		const catalogId = item.catalogId;

		if (fileMap.has(catalogId)) {
			fileMap.get(catalogId)!.push(item);
		} else {
			fileMap.set(catalogId, [item]);
		}
	});

	// 递归生成树状数据
	return fusionDigui(catalogData, fileMap);
};

// 递归生成树状数据
const fusionDigui = (catalogData: Array<ExportRecordCatalogOutput>, fileMap: Map<number, Array<any>>): Array<QueryRecordCheckListOutput> => {
	let enemData = [] as Array<QueryRecordCheckListOutput>;

	catalogData.forEach((item) => {
		// 档案目录
		let catalog = {
			id: item.id,
			createTime: item.createTime,
			updateTime: item.updateTime,
			createUserId: item.createUserId,
			createUserName: item.createUserName,
			updateUserId: item.updateUserId,
			updateUserName: item.updateUserName,
			isDelete: item.isDelete,
			orgId: item.orgId,
			fatherId: item.fatherId, // 父Id
			children: null, // 子集
			type: RefordFileEnum.CATALOG, // 类型

			name: item.name,
		} as QueryRecordCheckListOutput;

		// 材料
		if (fileMap.has(item.id)) {
			var fileArr = fileMap.get(item.id) ?? [];
			var children = [] as Array<QueryRecordCheckListOutput>;

			fileArr.forEach((file) => {
				children.push({
					id: file.id,
					createTime: file.createTime,
					updateTime: file.updateTime,
					createUserId: file.createUserId,
					createUserName: file.createUserName,
					updateUserId: file.updateUserId,
					updateUserName: file.updateUserName,
					isDelete: file.isDelete,
					orgId: file.orgId,
					fatherId: file.fatherId, // 父Id
					children: null, // 子集
					type: !file.fileIsSupplementData ? RefordFileEnum.FILE : RefordFileEnum.SUPPLEMENT_FILE, // 类型

					name: file.fileName,
					fileTime: file.fileTime,
					fileUnit: file.fileUnit,
					fileNumber: file.fileNumber,
					filePages: file.filePages,
					fileCoverPages: file.fileCoverPages,
					fileIsCopyData: file.fileIsCopyData,
					fileIsSupplementData: file.fileIsSupplementData,
					fileSupplementType: file.fileSupplementType,
					fileContent: file.fileContent,
					fileIsDigitization: file.fileIsDigitization,
					adminName: file.adminName,
					weigh: file.weigh,
				} as QueryRecordCheckListOutput);
			});

			catalog.children = children;
		}

		// 递归
		if (item.children != null && item.children.length > 0) {
			let catalogDataChildren = fusionDigui(item.children, fileMap);

			if (catalog.children != null) {
				catalog.children.push(...catalogDataChildren);
			} else {
				catalog.children = catalogDataChildren;
			}
		}

		enemData.push(catalog);
	});

	return enemData;
};
</script>
<template>
	<div>
		<el-button-group>
			<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordCheckList:page'" v-reclick="1000"> 查询 </el-button>
			<el-button
				type="primary"
				style="margin-left: 5px"
				icon="ele-Plus"
				@click="editDialogRef.openDialog(null, '新增档案清单与补充材料', props.recordId, props.catalogData)"
				v-auth="'recordCheckList:add'"
			>
				新增
			</el-button>
		</el-button-group>

		<el-card class="full-table" shadow="hover" style="margin-right: 8px; height: 700px; margin-top: 15px">
			<el-table
				:data="state.enemData"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				:tree-props="{ children: 'children', hasChildren: 'fatherId', checkStrictly: true }"
				default-expand-all
				border
			>
				<el-table-column prop="name" label="名称" width="500" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.type != RefordFileEnum.CATALOG" style="margin-right: 5px">
							<el-tag v-if="scope.row.type == RefordFileEnum.FILE"> 材 </el-tag>
							<el-tag v-else-if="scope.row.type == RefordFileEnum.SUPPLEMENT_FILE" type="warning"> 补 </el-tag>
							<span v-else></span>
						</span>
						<span>{{ scope.row.name }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="fileTime" label="形成时间" width="140" show-overflow-tooltip />
				<el-table-column prop="fileUnit" label="单位" show-overflow-tooltip />
				<el-table-column prop="fileNumber" label="数量" show-overflow-tooltip />
				<el-table-column prop="filePages" label="页数" show-overflow-tooltip />
				<el-table-column prop="fileIsCopyData" label="是否原件" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.type != RefordFileEnum.CATALOG">
							<el-tag v-if="scope.row.fileIsCopyData"> 是 </el-tag>
							<el-tag type="danger" v-else> 否 </el-tag>
						</span>
					</template>
				</el-table-column>
				<el-table-column prop="fileIsDigitization" label="数字化标识" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.type != RefordFileEnum.CATALOG">
							<el-tag v-if="scope.row.fileIsDigitization"> 是 </el-tag>
							<el-tag type="danger" v-else> 否 </el-tag>
						</span>
					</template>
				</el-table-column>
				<el-table-column prop="fileContent" label="备注" show-overflow-tooltip />
				<el-table-column prop="adminName" label="录入人" show-overflow-tooltip />
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" v-if="scope.row.type != RefordFileEnum.CATALOG" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('recordCheckList:update') || auth('recordCheckList:delete')">
					<template #default="scope">
						<div v-if="scope.row.type != RefordFileEnum.CATALOG">
							<el-button
								icon="ele-Edit"
								size="small"
								text
								type="primary"
								@click="editDialogRef.openDialog(scope.row, '编辑档案清单与补充材料', props.recordId, props.catalogData)"
								v-auth="'recordCheckList:update'"
							>
								编辑
							</el-button>
							<el-button icon="ele-Delete" size="small" text type="primary" @click="delRecordCheckList(scope.row)" v-auth="'recordCheckList:delete'"> 删除 </el-button>
						</div>
					</template>
				</el-table-column>
			</el-table>
			<ImportData ref="importDataRef" :import="recordCheckListApi.importData" :download="recordCheckListApi.downloadTemplate" v-auth="'recordCheckList:import'" @refresh="handleQuery" />
			<printDialog ref="printDialogRef" :title="'打印档案清单与补充材料'" @reloadTable="handleQuery" />
			<editDialog ref="editDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
