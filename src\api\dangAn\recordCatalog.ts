﻿import {useBaseApi} from '/@/api/base';

// 档案目录接口服务
export const useRecordCatalogApi = () => {
	const baseApi = useBaseApi("recordCatalog");
	return {
		// 分页查询档案目录
		page: baseApi.page,
		// 查看档案目录详细
		detail: baseApi.detail,
		// 新增档案目录
		add: baseApi.add,
		// 更新档案目录
		update: baseApi.update,
		// 删除档案目录
		delete: baseApi.delete,
		// 批量删除档案目录
		batchDelete: baseApi.batchDelete,
		// 导出档案目录数据
		exportData: baseApi.exportData,
		// 导入档案目录数据
		importData: baseApi.importData,
		// 下载档案目录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,

		// 树状查询档案柜
		query: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "query",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 档案目录实体
export interface RecordCatalog {
	// 主键Id
	id: number;
	// 父Id
	fatherId?: number;
	// 名称
	name?: string;
	// 描述
	description: string;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}