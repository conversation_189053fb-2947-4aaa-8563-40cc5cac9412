/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { KeyValuePairStringString } from './key-value-pair-string-string';
 /**
 * 接口压测输入参数
 *
 * @export
 * @interface StressTestInput
 */
export interface StressTestInput {

    /**
     * 接口请求地址
     *
     * @type {string}
     * @memberof StressTestInput
     * @example https://gitee.com/zuohuaijun/Admin.NET
     */
    requestUri: string;

    /**
     * 请求方式
     *
     * @type {string}
     * @memberof StressTestInput
     */
    requestMethod: string;

    /**
     * 每轮请求量
     *
     * @type {number}
     * @memberof StressTestInput
     * @example 100
     */
    numberOfRequests: number;

    /**
     * 压测轮数
     *
     * @type {number}
     * @memberof StressTestInput
     * @example 5
     */
    numberOfRounds: number;

    /**
     * 最大并行量（默认为当前主机逻辑处理器的数量）
     *
     * @type {number}
     * @memberof StressTestInput
     * @example 500
     */
    maxDegreeOfParallelism?: number | null;

    /**
     * 请求参数
     *
     * @type {Array<KeyValuePairStringString>}
     * @memberof StressTestInput
     */
    requestParameters?: Array<KeyValuePairStringString> | null;

    /**
     * 请求头参数
     *
     * @type {{ [key: string]: string; }}
     * @memberof StressTestInput
     */
    headers?: { [key: string]: string; } | null;

    /**
     * 路径参数
     *
     * @type {{ [key: string]: string; }}
     * @memberof StressTestInput
     */
    pathParameters?: { [key: string]: string; } | null;

    /**
     * Query参数
     *
     * @type {{ [key: string]: string; }}
     * @memberof StressTestInput
     */
    _queryParameters?: { [key: string]: string; } | null;
}
