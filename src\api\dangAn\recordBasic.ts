﻿import {useBaseApi} from '/@/api/base';

// 档案基本信息接口服务
export const useRecordBasicApi = () => {
	const baseApi = useBaseApi("recordBasic");
	return {
		// 分页查询档案基本信息
		page: baseApi.page,
		// 查看档案基本信息详细
		detail: baseApi.detail,
		// 新增档案基本信息
		add: baseApi.add,
		// 更新档案基本信息
		update: baseApi.update,
		// 删除档案基本信息
		delete: baseApi.delete,
		// 批量删除档案基本信息
		batchDelete: baseApi.batchDelete,
		// 导出档案基本信息数据
		exportData: baseApi.exportData,
		// 导入档案基本信息数据
		importData: baseApi.importData,
		// 下载档案基本信息数据导入模板
		downloadTemplate: baseApi.downloadTemplate,

		// 设置库柜层
		setChannel: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "setChannel",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 档案基本信息实体
export interface RecordBasic {
	// 主键Id
	id: number;
	// 姓名
	name?: string;
	// 曾用名
	formerName: string;
	// 证件类型
	identificationType?: number;
	// 证件号码
	identificationNumber?: string;
	// 出生日期
	birthday?: string;
	// 性别
	gender?: number;
	// 民族
	nationality?: number;
	// 委托原因
	entrustReason: string;
	// 出生地
	birthplace: string;
	// 籍贯
	censusRegister: string;
	// 户口编号
	registerNumber: string;
	// 婚姻状况
	maritalStatus: number;
	// 政治面貌
	politicsStatus?: number;
	// 分支机构
	reasonForProxy: number;
	// 个人身份
	userType: string;
	// 委托方式
	proxyType?: string;
	// 原档案号
	recordNumberOld: string;
	// 家庭地址
	homeAddress: string;
	// 手机
	mobile?: string;
	// 办公电话
	officeTel: string;
	// 家庭电话
	homeTel: string;
	// 其他号码
	otherContacts: string;
	// 电子邮箱
	email: string;
	// 家庭邮编
	homeCode: string;
	// 紧急联系人
	emergencyContact: string;
	// 与本人关系
	relationship: string;
	// 联系人手机号
	contactMobile: string;
	// 联系人电话
	contactTel: string;
	// 邮政编码
	postalCode: string;
	// 通信地址
	communicationAddress: string;
	// 系统识别码
	cystemIdentificationCode: string;
	// 参加工作时间
	worktime: number;
	// 户籍区划代码
	birthplaceCityCode?: number;
	// 健康状况
	healthStatus: string;
	// 语种
	language: string;
	// 语种熟练度
	languageProficient: number;
	// 宗教信仰
	belief: string;
	// 工作职位(岗位)类型
	companyPositionType: number;
	// 工作单位ID
	companyId: number;
	// 单位名称
	companyName: string;
	// 户口所在地址
	rigisterAddress: string;
	// 档案状态
	recordStatus: number;
	// 物理地址
	physicalAddress: string;
	// 所在库
	channelOneId: number;
	// 所在柜
	channelTwoId: number;
	// 所在层
	channelThreeId: number;
	// 毕业时间
	graduateTime: string;
	// 毕业学校
	graduateSchool: string;
	// 所学专业
	graduateMajor: string;
	// 学历
	personEdu?: string;
	// 学历性质
	eduNature: string;
	// 入党团时间
	politicsTime: string;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}