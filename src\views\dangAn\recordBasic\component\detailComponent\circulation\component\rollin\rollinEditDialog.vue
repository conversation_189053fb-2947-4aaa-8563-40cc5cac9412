﻿<script lang="ts" name="recordRollin" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordRollinApi } from '/@/api/dangAn/recordRollin';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordRollinApi = useRecordRollinApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,

	recordId: null as Number | null, // 详细id
});

// 自行添加其他规则
const rules = ref<FormRules>({
  recordId: [{required: true, message: '档案号不能为空！', trigger: 'blur',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string, recordId: Number) => {
	state.title = title;
	row = row ?? {  };

	if(row.recordId != null){
	  state.recordId = row.recordId;
      let list =  await recordRollinApi.queryRecordId({recordId: row.recordId}).then(res => res.data.result) ?? [];
      if(list != null && list.length > 0) state.ruleForm = list[0];
      else state.ruleForm = JSON.parse(JSON.stringify(row));
    }else {
      state.ruleForm = JSON.parse(JSON.stringify(row));
	  state.ruleForm.recordId = recordId;
	  state.recordId = recordId;

	  if(recordId == null){
		ElMessage({
			message: `档案号不能为空！`,
			type: "error",
		});
		state.showDialog = false;
		return;
	  }
    }
	
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;

			values.recordId = state.recordId;

			await recordRollinApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

// 日期限制
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordRollin-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案号" prop="recordId">
							<el-input v-model="state.ruleForm.recordId" placeholder="请输入档案号" maxlength="20" show-word-limit clearable disabled />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="原档案所在地" prop="oldOrganization">
							<el-input v-model="state.ruleForm.oldOrganization" placeholder="请输入原档案所在地" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="原档案所在机构地址" prop="oldOrganizationAddress">
							<el-input v-model="state.ruleForm.oldOrganizationAddress" placeholder="请输入原档案所在机构地址" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="原档案所在机构联系方式" prop="oldOrganizationTel">
							<el-input v-model="state.ruleForm.oldOrganizationTel" placeholder="请输入原档案所在机构联系方式" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案到达时间" prop="recordArrivalTime">
							<el-date-picker v-model="state.ruleForm.recordArrivalTime" type="date" placeholder="档案到达时间" :disabled-date="disabledDate" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转入机构名称" prop="inOrganization">
							<el-input v-model="state.ruleForm.inOrganization" placeholder="请输入转入机构名称" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转入原因" prop="inReason">
							<g-sys-dict v-model="state.ruleForm.inReason" code="Circulation_reason" render-as="select" placeholder="请选择转入原因" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="个人身份" prop="personageType">
							<g-sys-dict v-model="state.ruleForm.personageType" code="User_type" render-as="select" placeholder="请选择个人身份" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="原企业性质" prop="oldCompanyNature">
							<g-sys-dict v-model="state.ruleForm.oldCompanyNature" code="Company_nature" render-as="select" placeholder="请选择原企业性质" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转入方式" prop="inType">
							<g-sys-dict v-model="state.ruleForm.inType" code="Circulation_type" render-as="select" placeholder="请选择转入方式" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="签订保管协议时间" prop="identificationTime">
							<el-date-picker v-model="state.ruleForm.identificationTime" type="date" placeholder="签订保管协议时间" :disabled-date="disabledDate" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转入机要号" prop="inConfidentialNumber">
							<el-input v-model="state.ruleForm.inConfidentialNumber" placeholder="请输入转入机要号" maxlength="20" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="办理人事代理时间" prop="humanAgencyTime">
							<el-date-picker v-model="state.ruleForm.humanAgencyTime" type="date" placeholder="办理人事代理时间" :disabled-date="disabledDate" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="人事代理办理人" prop="humanAgencyPerson">
							<el-input v-model="state.ruleForm.humanAgencyPerson" placeholder="请输入人事代理办理人" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="回执情况" prop="receiptCondition">
							<g-sys-dict v-model="state.ruleForm.receiptCondition" code="Receipt_condition" render-as="select" placeholder="请选择回执情况" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="因执寄出日期" prop="outReceiptTime">
							<el-date-picker v-model="state.ruleForm.outReceiptTime" type="date" placeholder="因执寄出日期" :disabled-date="disabledDate" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="回执寄往单位" prop="outReceiptCompany">
							<el-input v-model="state.ruleForm.outReceiptCompany" placeholder="请输入回执寄往单位" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="回执寄往地址" prop="outReceiptAddress">
							<el-input v-model="state.ruleForm.outReceiptAddress" placeholder="请输入回执寄往地址" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="回执接收人" prop="receiptPerson">
							<el-input v-model="state.ruleForm.receiptPerson" placeholder="请输入回执接收人" maxlength="20" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="回执接收电话" prop="receiptTel">
							<el-input v-model="state.ruleForm.receiptTel" placeholder="请输入回执接收电话" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="邮编" prop="receiptPostalCode">
							<el-input-number v-model="state.ruleForm.receiptPostalCode" placeholder="请输入邮编" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="接收人员" prop="personIn">
							<el-input v-model="state.ruleForm.personIn" placeholder="请输入接收人员" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>