﻿<script lang="ts" name="recordBasic" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordBasicUpLogApi } from '/@/api/dangAn/recordBasicUpLog';


//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordBasicUpLogApi = useRecordBasicUpLogApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});
const theOriginalCollapse = ref(['1'])


// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await recordBasicUpLogApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordBasic-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-descriptions :column="3" border size="small" label-width="120px">
				<el-descriptions-item label="档案号">{{state.ruleForm.recordId}}</el-descriptions-item>
				<el-descriptions-item label="客户姓名">{{state.ruleForm.userName}}</el-descriptions-item>
				<el-descriptions-item label="修改时间">{{state.ruleForm.recordUpdateTime}}</el-descriptions-item>
				<el-descriptions-item label="修改者Id">{{state.ruleForm.recordUpdateUserId}}</el-descriptions-item>
				<el-descriptions-item label="修改者姓名">{{state.ruleForm.recordUpdateUserName}}</el-descriptions-item>
				<el-descriptions-item label="备注">{{state.ruleForm.details}}</el-descriptions-item>
				<el-descriptions-item label="原本内容" :span="3">
					<el-collapse>
						<el-collapse-item title="查看">
							<div>{{state.ruleForm.theOriginal}}</div>
						</el-collapse-item>
					</el-collapse>
				</el-descriptions-item>
				<el-descriptions-item label="修改后的内容" :span="3">
					<el-collapse>
						<el-collapse-item title="查看">
							<div>{{state.ruleForm.newOriginal}}</div>
						</el-collapse-item>
					</el-collapse>
				</el-descriptions-item>
			</el-descriptions>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">关 闭</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>