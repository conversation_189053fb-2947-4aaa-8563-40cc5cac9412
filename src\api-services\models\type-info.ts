/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Assembly } from './assembly';
import { ConstructorInfo } from './constructor-info';
import { CustomAttributeData } from './custom-attribute-data';
import { EventInfo } from './event-info';
import { FieldInfo } from './field-info';
import { GenericParameterAttributes } from './generic-parameter-attributes';
import { MemberInfo } from './member-info';
import { MemberTypes } from './member-types';
import { MethodBase } from './method-base';
import { MethodInfo } from './method-info';
import { Module } from './module';
import { PropertyInfo } from './property-info';
import { RuntimeTypeHandle } from './runtime-type-handle';
import { StructLayoutAttribute } from './struct-layout-attribute';
import { Type } from './type';
import { TypeAttributes } from './type-attributes';
import { TypeInfo } from './type-info';
 /**
 * 
 *
 * @export
 * @interface TypeInfo
 */
export interface TypeInfo {

    /**
     * @type {string}
     * @memberof TypeInfo
     */
    name?: string | null;

    /**
     * @type {Array<CustomAttributeData>}
     * @memberof TypeInfo
     */
    customAttributes?: Array<CustomAttributeData> | null;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isCollectible?: boolean;

    /**
     * @type {number}
     * @memberof TypeInfo
     */
    metadataToken?: number;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isInterface?: boolean;

    /**
     * @type {MemberTypes}
     * @memberof TypeInfo
     */
    memberType?: MemberTypes;

    /**
     * @type {string}
     * @memberof TypeInfo
     */
    namespace?: string | null;

    /**
     * @type {string}
     * @memberof TypeInfo
     */
    assemblyQualifiedName?: string | null;

    /**
     * @type {string}
     * @memberof TypeInfo
     */
    fullName?: string | null;

    /**
     * @type {Assembly}
     * @memberof TypeInfo
     */
    assembly?: Assembly;

    /**
     * @type {Module}
     * @memberof TypeInfo
     */
    module?: Module;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isNested?: boolean;

    /**
     * @type {Type}
     * @memberof TypeInfo
     */
    declaringType?: Type;

    /**
     * @type {MethodBase}
     * @memberof TypeInfo
     */
    declaringMethod?: MethodBase;

    /**
     * @type {Type}
     * @memberof TypeInfo
     */
    reflectedType?: Type;

    /**
     * @type {Type}
     * @memberof TypeInfo
     */
    underlyingSystemType?: Type;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isTypeDefinition?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isArray?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isByRef?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isPointer?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isConstructedGenericType?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isGenericParameter?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isGenericTypeParameter?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isGenericMethodParameter?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isGenericType?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isGenericTypeDefinition?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isSZArray?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isVariableBoundArray?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isByRefLike?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isFunctionPointer?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isUnmanagedFunctionPointer?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    hasElementType?: boolean;

    /**
     * @type {Array<Type>}
     * @memberof TypeInfo
     */
    genericTypeArguments?: Array<Type> | null;

    /**
     * @type {number}
     * @memberof TypeInfo
     */
    genericParameterPosition?: number;

    /**
     * @type {GenericParameterAttributes}
     * @memberof TypeInfo
     */
    genericParameterAttributes?: GenericParameterAttributes;

    /**
     * @type {TypeAttributes}
     * @memberof TypeInfo
     */
    attributes?: TypeAttributes;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isAbstract?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isImport?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isSealed?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isSpecialName?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isClass?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isNestedAssembly?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isNestedFamANDAssem?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isNestedFamily?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isNestedFamORAssem?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isNestedPrivate?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isNestedPublic?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isNotPublic?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isPublic?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isAutoLayout?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isExplicitLayout?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isLayoutSequential?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isAnsiClass?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isAutoClass?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isUnicodeClass?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isCOMObject?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isContextful?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isEnum?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isMarshalByRef?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isPrimitive?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isValueType?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isSignatureType?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isSecurityCritical?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isSecuritySafeCritical?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isSecurityTransparent?: boolean;

    /**
     * @type {StructLayoutAttribute}
     * @memberof TypeInfo
     */
    structLayoutAttribute?: StructLayoutAttribute;

    /**
     * @type {ConstructorInfo}
     * @memberof TypeInfo
     */
    typeInitializer?: ConstructorInfo;

    /**
     * @type {RuntimeTypeHandle}
     * @memberof TypeInfo
     */
    typeHandle?: RuntimeTypeHandle;

    /**
     * @type {string}
     * @memberof TypeInfo
     */
    guid?: string;

    /**
     * @type {Type}
     * @memberof TypeInfo
     */
    baseType?: Type;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isSerializable?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    containsGenericParameters?: boolean;

    /**
     * @type {boolean}
     * @memberof TypeInfo
     */
    isVisible?: boolean;

    /**
     * @type {Array<Type>}
     * @memberof TypeInfo
     */
    genericTypeParameters?: Array<Type> | null;

    /**
     * @type {Array<ConstructorInfo>}
     * @memberof TypeInfo
     */
    declaredConstructors?: Array<ConstructorInfo> | null;

    /**
     * @type {Array<EventInfo>}
     * @memberof TypeInfo
     */
    declaredEvents?: Array<EventInfo> | null;

    /**
     * @type {Array<FieldInfo>}
     * @memberof TypeInfo
     */
    declaredFields?: Array<FieldInfo> | null;

    /**
     * @type {Array<MemberInfo>}
     * @memberof TypeInfo
     */
    declaredMembers?: Array<MemberInfo> | null;

    /**
     * @type {Array<MethodInfo>}
     * @memberof TypeInfo
     */
    declaredMethods?: Array<MethodInfo> | null;

    /**
     * @type {Array<TypeInfo>}
     * @memberof TypeInfo
     */
    declaredNestedTypes?: Array<TypeInfo> | null;

    /**
     * @type {Array<PropertyInfo>}
     * @memberof TypeInfo
     */
    declaredProperties?: Array<PropertyInfo> | null;

    /**
     * @type {Array<Type>}
     * @memberof TypeInfo
     */
    implementedInterfaces?: Array<Type> | null;
}
