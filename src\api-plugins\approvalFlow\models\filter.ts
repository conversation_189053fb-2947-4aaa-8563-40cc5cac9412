/* tslint:disable */
/* eslint-disable */
/**
 * ApprovalFlow
 * <br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { Filter } from './filter';
import { FilterLogicEnum } from './filter-logic-enum';
import { FilterOperatorEnum } from './filter-operator-enum';
/**
 * 筛选过滤条件
 * @export
 * @interface Filter
 */
export interface Filter {
    /**
     * 
     * @type {FilterLogicEnum}
     * @memberof Filter
     */
    logic?: FilterLogicEnum;
    /**
     * 筛选过滤条件子项
     * @type {Array<Filter>}
     * @memberof Filter
     */
    filters?: Array<Filter> | null;
    /**
     * 字段名称
     * @type {string}
     * @memberof Filter
     */
    field?: string | null;
    /**
     * 
     * @type {FilterOperatorEnum}
     * @memberof Filter
     */
    operator?: FilterOperatorEnum;
    /**
     * 字段值
     * @type {any}
     * @memberof Filter
     */
    value?: any | null;
}
