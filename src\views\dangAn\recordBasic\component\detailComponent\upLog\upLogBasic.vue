<script lang="ts" setup name="recordBasic">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useRecordBasicUpLogApi } from '/@/api/dangAn/recordBasicUpLog';
import upLogDetailDialog from './upLogDetailDialog.vue'


const recordBasicUpLogApi = useRecordBasicUpLogApi();
const upLogDetailDialogRef = ref();

//父级传递来的数据
const props = defineProps({
  recordId: { // 档案Id
    type: Number,
    required: true,
    default: null
  },
})

const state = reactive({
  exportLoading: false,
  tableLoading: false,

  tableQueryParams: {} as any, // 查询条件
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [], // 列表数据
  selectData: [] as any[],
});

// 页面加载时
onMounted(async () => {
    await handleQuery();
});

// 查询操作
const handleQuery = async (params: any = {}) => {
    state.tableLoading = true;

    state.tableQueryParams.recordId = props.recordId;
    const result = await recordBasicUpLogApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
    state.tableParams.total = result?.total;
    state.tableData = result?.items ?? [];

    state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};


//将属性或者函数暴露给父组件
defineExpose({ handleQuery });
</script>
<template>
  <div>
    <el-card shadow="hover" style="margin-top: 5px;padding-bottom: 10px;">
        <template #header>
            <div class="card-header">
                <span>档案号：{{props.recordId}}&nbsp;&nbsp;&nbsp;&nbsp;修改次数：{{state.tableData.length}}</span>
                <span style="margin-left: 20px;"><el-button type="primary"  icon="ele-RefreshRight" @click="handleQuery" v-auth="'recordBasic:page'" v-reclick="1000" /></span>
            </div>
        </template>
        
        <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }" style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id" @sort-change="sortChange" border>
        <el-table-column type="selection" width="40" align="center" v-if="auth('recordBasic:batchDelete') || auth('recordBasic:export')" />
        <el-table-column type="index" label="序号" width="55" align="center"/>
        <el-table-column prop='recordId' label='档案号' show-overflow-tooltip />
        <el-table-column prop='userName' label='客户姓名' show-overflow-tooltip />
        <el-table-column prop='recordUpdateTime' label='修改时间' show-overflow-tooltip />
        <el-table-column prop='recordUpdateUserName' label='修改者姓名' show-overflow-tooltip />
        <el-table-column prop='details' label='备注' show-overflow-tooltip />
        <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('recordBasic:update') || auth('recordBasic:delete')">
          <template #default="scope">
            <el-button icon="ele-Document" size="small" text type="primary" @click="upLogDetailDialogRef.openDialog(scope.row, `档案${props.recordId}修改记录详细`)" v-auth="'recordBasic:detail'"> 详细 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination 
              v-model:currentPage="state.tableParams.page"
              v-model:page-size="state.tableParams.pageSize"
              @size-change="(val: any) => handleQuery({ pageSize: val })"
              @current-change="(val: any) => handleQuery({ page: val })"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100, 200, 500]"
              :total="state.tableParams.total"
              size="small"
              background />
      <upLogDetailDialog ref="upLogDetailDialogRef"/>
    </el-card>
  </div>
</template>
<style scoped>
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}

.linkedList :deep(.el-radio-button__inner){
  width: 100%;
}
</style>