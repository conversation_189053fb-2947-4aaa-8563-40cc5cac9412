﻿<script lang="ts" name="recordLend" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordLendApi } from '/@/api/dangAn/recordLend';


//父级传递来的数据
const props = defineProps({
  recordId: { // 档案Id
    type: Number,
    required: true,
    default: null
  },
})

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordLendApi = useRecordLendApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
  recordId: [{required: true, message: '请选择档案号！', trigger: 'blur',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await recordLendApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.ruleForm.recordId = props.recordId;


	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			values.recordId = props.recordId;

			await recordLendApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordLend-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案号" prop="recordId">
							<el-input v-model="state.ruleForm.recordId" placeholder="请输入档案号" maxlength="20" show-word-limit clearable disabled />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="申请日期" prop="applyTime">
							<el-date-picker v-model="state.ruleForm.applyTime" type="date" placeholder="申请日期" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="业务办理日期" prop="handleTime">
							<el-date-picker v-model="state.ruleForm.handleTime" type="date" placeholder="业务办理日期" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="业务类型" prop="lendType">
							<g-sys-dict v-model="state.ruleForm.lendType" code="ConsultTypeEnum" render-as="select" placeholder="请选择业务类型" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="数量" prop="fileNum">
							<el-input-number v-model="state.ruleForm.fileNum" placeholder="请输入数量" :precision="0" max="1" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="单位" prop="fileUnit">
							<el-select v-model="state.ruleForm.fileUnit" placeholder="请选择单位" style="width: 240px">
								<el-option label="份" value="份" />
								<el-option label="张" value="张" />
								<el-option label="页" value="页" />
							</el-select>
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="申请人" prop="applyName">
							<el-input v-model="state.ruleForm.applyName" placeholder="请输入申请人" maxlength="10" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="使用人" prop="useName">
							<el-input v-model="state.ruleForm.useName" placeholder="请输入使用人" maxlength="10" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="使用单位" prop="useCompany">
							<el-input v-model="state.ruleForm.useCompany" placeholder="请输入使用单位" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="借阅内容" prop="consultContent">
							<g-sys-dict v-model="state.ruleForm.consultContent" code="Consult_content" render-as="select" placeholder="请选择借阅内容" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="借出用途" prop="lendPurpose">
							<el-input v-model="state.ruleForm.lendPurpose" placeholder="请输入借出用途" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="联系方式" prop="telephone">
							<el-input v-model="state.ruleForm.telephone" placeholder="请输入联系方式" maxlength="20" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="申请备注" prop="applyRemarks">
							<el-input v-model="state.ruleForm.applyRemarks" placeholder="请输入申请备注" maxlength="255" type="textarea" :rows="2" show-word-limit clearable />
						</el-form-item>
					</el-col>



					<el-divider />



						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案归还人" prop="returnPerson">
							<el-input v-model="state.ruleForm.returnPerson" placeholder="请输入档案归还人" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="业务办结人" prop="completionPerson">
							<el-input v-model="state.ruleForm.completionPerson" placeholder="请输入业务办结人" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="业务办结日期" prop="completionTime">
							<el-date-picker v-model="state.ruleForm.completionTime" type="date" placeholder="业务办结日期" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="办结备注" prop="completionRemarks">
							<el-input v-model="state.ruleForm.completionRemarks" placeholder="请输入办结备注" type="textarea" :rows="2" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>