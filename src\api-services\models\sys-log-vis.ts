/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { LogLevel } from './log-level';
 /**
 * 系统访问日志表
 *
 * @export
 * @interface SysLogVis
 */
export interface SysLogVis {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof SysLogVis
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof SysLogVis
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof SysLogVis
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof SysLogVis
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof SysLogVis
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof SysLogVis
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof SysLogVis
     */
    updateUserName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof SysLogVis
     */
    tenantId?: number | null;

    /**
     * 模块名称
     *
     * @type {string}
     * @memberof SysLogVis
     */
    controllerName?: string | null;

    /**
     * 方法名称
     *
     * @type {string}
     * @memberof SysLogVis
     */
    actionName?: string | null;

    /**
     * 显示名称
     *
     * @type {string}
     * @memberof SysLogVis
     */
    displayTitle?: string | null;

    /**
     * 执行状态
     *
     * @type {string}
     * @memberof SysLogVis
     */
    status?: string | null;

    /**
     * IP地址
     *
     * @type {string}
     * @memberof SysLogVis
     */
    remoteIp?: string | null;

    /**
     * 登录地点
     *
     * @type {string}
     * @memberof SysLogVis
     */
    location?: string | null;

    /**
     * 经度
     *
     * @type {number}
     * @memberof SysLogVis
     */
    longitude?: number | null;

    /**
     * 维度
     *
     * @type {number}
     * @memberof SysLogVis
     */
    latitude?: number | null;

    /**
     * 浏览器
     *
     * @type {string}
     * @memberof SysLogVis
     */
    browser?: string | null;

    /**
     * 操作系统
     *
     * @type {string}
     * @memberof SysLogVis
     */
    os?: string | null;

    /**
     * 操作用时
     *
     * @type {number}
     * @memberof SysLogVis
     */
    elapsed?: number | null;

    /**
     * 日志时间
     *
     * @type {Date}
     * @memberof SysLogVis
     */
    logDateTime?: Date | null;

    /**
     * @type {LogLevel}
     * @memberof SysLogVis
     */
    logLevel?: LogLevel;

    /**
     * 账号
     *
     * @type {string}
     * @memberof SysLogVis
     */
    account?: string | null;

    /**
     * 真实姓名
     *
     * @type {string}
     * @memberof SysLogVis
     */
    realName?: string | null;
}
