﻿<script lang="ts" setup name="recordBasic">
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordCatalogApi } from '/@/api/dangAn/recordCatalog';
import { useRecordChannelApi } from '/@/api/dangAn/recordChannel';
import detailBasic from './detailComponent/basic/detailBasic.vue';
import detailRecord from './detailComponent/basic/record/detailRecord.vue';
import detailCirculation from './detailComponent/circulation/detailCirculation.vue';
import recordFiles from './detailComponent/files/recordFiles.vue';
import yueBasic from './detailComponent/yue/detailYue.vue';
import upLogBasic from './detailComponent/upLog/upLogBasic.vue';
import { QueryRecordChanneOutput } from '/@/api-services/dangAn/recordChannel';
import { ExportRecordCatalogOutput } from '/@/api-services/dangAn/recordCatalog';

const recordChannelApi = useRecordChannelApi();
const recordCatalogApi = useRecordCatalogApi();
const route = useRoute();
const state = reactive({
	exportLoading: false, // 加载
	channelData: [] as Array<QueryRecordChanneOutput>, // 档案柜数据
	catalogData: [] as Array<ExportRecordCatalogOutput>, // 档案目录数据
});
const recordId = computed(() => +(route.query.recordId ?? 0));

// 页面加载时
onMounted(async () => {
	state.exportLoading = true;

	// 档案柜数据
	state.channelData = (await recordChannelApi.queryEnable({}).then((res) => res.data.result)) ?? [];

	// 档案目录数据
	state.catalogData = (await recordCatalogApi.query({}).then((res) => res.data.result)) ?? [];

	state.exportLoading = false;
});
</script>

<template>
	<div class="recordBasic-container" v-loading="state.exportLoading">
		<el-tabs v-if="recordId" tab-position="left" type="border-card" style="height: 100%">
			<el-tab-pane label="基本信息">
				<detailBasic :recordId="recordId" />
				<detailRecord :recordId="recordId" />
			</el-tab-pane>

			<el-tab-pane label="档案流转">
				<detailCirculation :recordId="recordId" :channelData="state.channelData" />
			</el-tab-pane>

			<el-tab-pane label="档案清单与补充材料">
				<recordFiles :recordId="recordId" :catalogData="state.catalogData" />
			</el-tab-pane>

			<el-tab-pane label="档案查阅、借阅记录">
				<yueBasic :recordId="recordId" />
			</el-tab-pane>

			<el-tab-pane label="档案修改记录">
				<upLogBasic :recordId="recordId" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
