<template>
  <div class="tree-path">
    <span v-if="pathText">{{ pathText }}</span>
    <span v-else class="no-data">未找到匹配项</span>
  </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue'

const props = defineProps({
  data: {
    type: Array as PropType<any[]>,
    required: true,
    default: () => []
  },
  children: {
    type: String,
    default: 'children'
  },
  label: {
    type: String,
    default: 'label'
  },
  hasChildren: {
    type: String,
    default: 'fatherId'
  },
  value: {
    type: Number,
    default: null
  }
})

// 创建ID到节点的映射
const nodeMap = computed(() => {
  const map = new Map<number, any>()
  
  const traverse = (nodes: any[]) => {
    if (!nodes || !Array.isArray(nodes)) return
    
    for (const node of nodes) {
      if (node.id !== undefined) {
        map.set(node.id, node)
      }
      
      if (node[props.children] && Array.isArray(node[props.children])) {
        traverse(node[props.children])
      }
    }
  }
  
  traverse(props.data)
  return map
})

// 计算路径文本
const pathText = computed(() => {
  if (!props.value) return ''
  
  const node = nodeMap.value.get(props.value)
  if (!node) return ''
  
  const path: string[] = []
  let currentNode = node
  
  // 向上查找父节点构建路径
  while (currentNode) {
    path.unshift(currentNode[props.label])
    
    const parentId = currentNode[props.hasChildren]
    if (parentId === null || parentId === undefined) break
    
    currentNode = nodeMap.value.get(parentId)
    if (!currentNode) break
  }
  
  return path.join(' / ')
})
</script>

<style scoped>
</style>