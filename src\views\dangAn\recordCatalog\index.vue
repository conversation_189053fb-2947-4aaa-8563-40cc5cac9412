﻿<script lang="ts" setup name="recordCatalog">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordCatalogApi } from '/@/api/dangAn/recordCatalog';
import editDialog from '/@/views/dangAn/recordCatalog/component/editDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from '/@/components/table/importData.vue';

const recordCatalogApi = useRecordCatalogApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
	exportLoading: false,
	tableLoading: false,
	stores: {},
	showAdvanceQueryUI: false,
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableData: [],
});

// 页面加载时
onMounted(async () => {});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	const result = await recordCatalogApi.query(Object.assign(state.tableQueryParams)).then((res) => res.data.result);
	state.tableData = result ?? [];
	state.tableLoading = false;
};

// 删除
const delRecordCatalog = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordCatalogApi.delete({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 批量删除
const batchDelRecordCatalog = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordCatalogApi.batchDelete(state.selectData.map((u) => ({ id: u.id }))).then((res) => {
				ElMessage.success(`成功批量删除${res.data.result}条记录`);
				handleQuery();
			});
		})
		.catch(() => {});
};

handleQuery();
</script>
<template>
	<div class="recordCatalog-container" v-loading="state.exportLoading">
		<div class="bg-white rounded-md pt-5 px-5 mb-4">
			<el-form :model="state.tableQueryParams" ref="queryForm" inline v-if="!state.showAdvanceQueryUI">
				<!-- 简单查询模式 -->
				<el-form-item label="关键字">
					<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
				</el-form-item>

				<el-form-item>
					<div style="display: flex; align-items: center; flex-wrap: nowrap; white-space: nowrap">
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordCatalog:page'" v-reclick="1000"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})" style="margin-left: 5px"> 重置 </el-button>
						<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" style="margin-left: 5px"> 高级查询 </el-button>
						<el-button type="danger" style="margin-left: 5px" icon="ele-Delete" @click="batchDelRecordCatalog" :disabled="state.selectData.length == 0" v-auth="'recordCatalog:batchDelete'">
							删除
						</el-button>
						<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef.openDialog(null, '新增档案目录')" v-auth="'recordCatalog:add'"> 新增 </el-button>
						<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'recordCatalog:import'"> 导入 </el-button>
					</div>
				</el-form-item>
			</el-form>

			<el-form :model="state.tableQueryParams" ref="queryForm" :label-width="120" v-if="state.showAdvanceQueryUI">
				<!-- 高级查询模式 - 3列布局 -->
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="名称">
							<el-input v-model="state.tableQueryParams.name" clearable placeholder="请输入名称" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item>
							<div class="flex items-center justify-end flex-wrap mt-4 w-full">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordCatalog:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})" style="margin-left: 5px"> 重置 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" style="margin-left: 5px"> 隐藏 </el-button>
								<el-button type="danger" style="margin-left: 5px" icon="ele-Delete" @click="batchDelRecordCatalog" :disabled="state.selectData.length == 0" v-auth="'recordCatalog:batchDelete'">
									删除
								</el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef.openDialog(null, '新增档案目录')" v-auth="'recordCatalog:add'"> 新增 </el-button>
								<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'recordCatalog:import'"> 导入 </el-button>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				:tree-props="{ children: 'children', hasChildren: 'fatherId' }"
				border
			>
				<el-table-column prop="name" label="名称" show-overflow-tooltip />
				<el-table-column prop="description" label="描述" show-overflow-tooltip />
				<el-table-column prop="weigh" label="权重" show-overflow-tooltip />
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('recordCatalog:update') || auth('recordCatalog:delete')">
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="editDialogRef.openDialog(scope.row, '编辑档案目录')" v-auth="'recordCatalog:update'"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="primary" @click="delRecordCatalog(scope.row)" v-auth="'recordCatalog:delete'"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<ImportData ref="importDataRef" :import="recordCatalogApi.importData" :download="recordCatalogApi.downloadTemplate" v-auth="'recordCatalog:import'" @refresh="handleQuery" />
			<printDialog ref="printDialogRef" :title="'打印档案目录'" @reloadTable="handleQuery" />
			<editDialog ref="editDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
