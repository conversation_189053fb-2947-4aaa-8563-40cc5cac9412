<script lang="ts" setup name="recordBasic">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useRecordRolloutApi } from '/@/api/dangAn/recordRollout';
import rolloutEditDialog from './rolloutEditDialog.vue'

//父级传递来的数据
const props = defineProps({
  recordId: { // 档案Id
    type: Number,
    required: true,
    default: null
  },
})

const recordRolloutApi = useRecordRolloutApi();
const editDialogRef = ref();
const state = reactive({
  exportLoading: false,
  tableLoading: false,

  rolloutData: {} as any, // 转出信息
});

// 页面加载时
onMounted(async () => {
  handleQuery();
});

// 查询操作
const handleQuery = async (params: any = {}) => {
    state.tableLoading = true;

    if(props.recordId != null){
      let list =  await recordRolloutApi.queryRecordId({recordId: props.recordId}).then(res => res.data.result) ?? [];
      if(list != null && list.length > 0) state.rolloutData = list[0];
      else state.rolloutData = {};
    }else {
      state.rolloutData = {};
    }

    state.tableLoading = false;
};


//将属性或者函数暴露给父组件
defineExpose({ handleQuery });
</script>
<template>
  <div>
    <el-descriptions :column="4" border size="small" label-width="120px">
        <el-descriptions-item label="档案号">{{state.rolloutData.recordId}}</el-descriptions-item>
        <el-descriptions-item label="转出申请时间">{{state.rolloutData.applyTime}}</el-descriptions-item>
        <el-descriptions-item label="转出状态">{{state.rolloutData.status}}</el-descriptions-item>
        <el-descriptions-item label="转出原因"><g-sys-dict v-model="state.rolloutData.reason" code="Circulation_reason" /></el-descriptions-item>
        <el-descriptions-item label="转至机构名称">{{state.rolloutData.organization}}</el-descriptions-item>
        <el-descriptions-item label="单位性质"><g-sys-dict v-model="state.rolloutData.companyNature" code="Company_nature" /></el-descriptions-item>
        <el-descriptions-item label="转出地址">{{state.rolloutData.address}}</el-descriptions-item>
        <el-descriptions-item label="档案接收人">{{state.rolloutData.recipient}}</el-descriptions-item>
        <el-descriptions-item label="电话">{{state.rolloutData.recipientTel}}</el-descriptions-item>
        <el-descriptions-item label="转出提醒时间">{{state.rolloutData.tipTime}}</el-descriptions-item>
        <el-descriptions-item label="转出表编号">{{state.rolloutData.serialNumber}}</el-descriptions-item>
        <el-descriptions-item label="转出机要号">{{state.rolloutData.confidentialNumber}}</el-descriptions-item>
        <el-descriptions-item label="档案转出时间">{{state.rolloutData.outTime}}</el-descriptions-item>
        <el-descriptions-item label="人事关系转出">{{state.rolloutData.humanAgencey}}</el-descriptions-item>
        <el-descriptions-item label="人事关系转出时间">{{state.rolloutData.humanAgenceyTime}}</el-descriptions-item>
        <el-descriptions-item label="行政介绍信">{{state.rolloutData.introductionLetter}}</el-descriptions-item>
        <el-descriptions-item label="行政介绍信出具人">{{state.rolloutData.introductionLetterPerson}}</el-descriptions-item>
        <el-descriptions-item label="行政介绍信开出日期">{{state.rolloutData.introductionLetterAddtime}}</el-descriptions-item>
        <el-descriptions-item label="工资介绍信">{{state.rolloutData.payrollLetter}}</el-descriptions-item>
        <el-descriptions-item label="工资介绍信出具人">{{state.rolloutData.payrollLetterPerson}}</el-descriptions-item>
        <el-descriptions-item label="邮编">{{state.rolloutData.postalCode}}</el-descriptions-item>
        <el-descriptions-item label="转出人员">{{state.rolloutData.personOut}}</el-descriptions-item>

    </el-descriptions>
    
    <el-button icon="ele-Edit" style="margin-top: 10px;" size="small" type="primary" @click="editDialogRef.openDialog(state.rolloutData, '编辑转入信息', props.recordId)" v-auth="'recordRollout:update'"> 编辑 </el-button>
        
    <rolloutEditDialog ref="editDialogRef" @reloadTable="handleQuery(state.rolloutData)" />
  </div>
</template>
<style scoped>
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}

.linkedList :deep(.el-radio-button__inner){
  width: 100%;
}
</style>