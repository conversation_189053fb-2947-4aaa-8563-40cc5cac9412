﻿<script lang="ts" name="recordBasic" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordBasicApi } from '/@/api/dangAn/recordBasic';
import { getBirthDateFromIdCard, getGenderFromIdCard, validateIdCard } from '/@/utils/myUtils/myUtils'


//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordBasicApi = useRecordBasicApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
  name: [{required: true, message: '请选择姓名！', trigger: 'blur',},],
  identificationType: [{required: true, message: '请选择证件类型！', trigger: 'change',},],
  identificationNumber: [{required: true, message: '请选择证件号码！', trigger: 'blur',},],
  birthday: [{required: true, message: '请选择出生日期！', trigger: 'change',},],
  gender: [{required: true, message: '请选择性别！', trigger: 'change',},],
  nationality: [{required: true, message: '请选择民族！', trigger: 'change',},],
  politicsStatus: [{required: true, message: '请选择政治面貌！', trigger: 'change',},],
  proxyType: [{required: true, message: '请选择委托方式！', trigger: 'change',},],
  mobile: [{required: true, message: '请选择手机！', trigger: 'blur',},],
  birthplaceCityCode: [{required: true, message: '请输入户籍区划代码！', trigger: 'blur',},],
  personEdu: [{required: true, message: '请选择学历！', trigger: 'change',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await recordBasicApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {

		// if(!validateIdCard(state.ruleForm.identificationNumber)){
		// 	ElMessage({
		// 		message: `请输入正确的证件格式`,
		// 		type: "error",
		// 	});
		// }

		if (isValid) {
			let values = state.ruleForm;
			await recordBasicApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

// 输入证件号处理
const inputIdentificationNumber = () => {
	if(state.ruleForm.identificationNumber != null && state.ruleForm.identificationNumber.length == 18){
		try {
			state.ruleForm.birthday = getBirthDateFromIdCard(state.ruleForm.identificationNumber); // 生日
			state.ruleForm.gender = getGenderFromIdCard(state.ruleForm.identificationNumber); // 性别
		} catch (error) {
		}
	}
}

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordBasic-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="姓名" prop="name">
							<el-input v-model="state.ruleForm.name" placeholder="请输入姓名" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="证件类型" prop="identificationType">
							<g-sys-dict v-model="state.ruleForm.identificationType" code="IdentificationTypeEnum" render-as="select" placeholder="请选择证件类型" @input="inputIdentificationNumber()" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="证件号码" prop="identificationNumber">
							<el-input v-model="state.ruleForm.identificationNumber" placeholder="请输入证件号码" maxlength="20" @input="inputIdentificationNumber()" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="出生日期" prop="birthday">
							<el-date-picker v-model="state.ruleForm.birthday" type="date" placeholder="出生日期" :disabled-date="disabledDate" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="性别" prop="gender">
							<g-sys-dict v-model="state.ruleForm.gender" code="GenderEnum" render-as="select" placeholder="请选择性别" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="民族" prop="nationality">
							<g-sys-dict v-model="state.ruleForm.nationality" code="NationalityEnum" render-as="select" placeholder="请选择民族" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="政治面貌" prop="politicsStatus">
							<g-sys-dict v-model="state.ruleForm.politicsStatus" code="PoliticsStatusEnum" render-as="select" placeholder="请选择政治面貌" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="委托方式" prop="proxyType">
							<g-sys-dict v-model="state.ruleForm.proxyType" code="Proxy_type" render-as="select" placeholder="请选择委托方式" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="手机" prop="mobile">
							<el-input v-model="state.ruleForm.mobile" placeholder="请输入手机" maxlength="11" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="户籍区划代码" prop="birthplaceCityCode">
							<el-input-number v-model="state.ruleForm.birthplaceCityCode" placeholder="请输入户籍区划代码" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="学历" prop="personEdu">
							<g-sys-dict v-model="state.ruleForm.personEdu" code="Person_edu" render-as="select" placeholder="请选择学历" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="录入人姓名" prop="adminName">
							<el-input v-model="state.ruleForm.adminName" placeholder="请输入录入人姓名" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>