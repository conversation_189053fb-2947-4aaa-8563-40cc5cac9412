﻿import {useBaseApi} from '/@/api/base';

// 档案转入详细记录接口服务
export const useRecordRollinApi = () => {
	const baseApi = useBaseApi("recordRollin");
	return {
		// 分页查询档案转入详细记录
		page: baseApi.page,
		// 查看档案转入详细记录详细
		detail: baseApi.detail,
		// 新增档案转入详细记录
		add: baseApi.add,
		// 更新档案转入详细记录
		update: baseApi.update,
		// 删除档案转入详细记录
		delete: baseApi.delete,
		// 批量删除档案转入详细记录
		batchDelete: baseApi.batchDelete,
		// 导出档案转入详细记录数据
		exportData: baseApi.exportData,
		// 导入档案转入详细记录数据
		importData: baseApi.importData,
		// 下载档案转入详细记录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,


		// 树状查询档案柜
		queryRecordId: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "queryRecordId",
                method: 'post',
                data,
            }, cancel);
        },

	}
}

// 档案转入详细记录实体
export interface RecordRollin {
	// 主键Id
	id: number;
	// 档案号
	recordId?: number;
	// 原档案所在地
	oldOrganization: string;
	// 原档案所在机构地址
	oldOrganizationAddress: string;
	// 原档案所在机构联系方式
	oldOrganizationTel: string;
	// 档案到达时间
	recordArrivalTime: string;
	// 转入机构名称
	inOrganization: string;
	// 转入原因
	inReason: string;
	// 个人身份
	personageType: string;
	// 原企业性质
	oldCompanyNature: string;
	// 转入方式
	inType: string;
	// 签订保管协议时间
	identificationTime: string;
	// 转入机要号
	inConfidentialNumber: string;
	// 办理人事代理时间
	humanAgencyTime: string;
	// 人事代理办理人
	humanAgencyPerson: string;
	// 回执情况
	receiptCondition: string;
	// 因执寄出日期
	outReceiptTime: string;
	// 回执寄往单位
	outReceiptCompany: string;
	// 回执寄往地址
	outReceiptAddress: string;
	// 回执接收人
	receiptPerson: string;
	// 回执接收电话
	receiptTel: string;
	// 邮编
	receiptPostalCode: number;
	// 接收人员
	personIn: string;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}