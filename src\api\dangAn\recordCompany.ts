﻿import {useBaseApi} from '/@/api/base';

// 企业管理接口服务
export const useRecordCompanyApi = () => {
	const baseApi = useBaseApi("recordCompany");
	return {
		// 分页查询企业管理
		page: baseApi.page,
		// 查看企业管理详细
		detail: baseApi.detail,
		// 新增企业管理
		add: baseApi.add,
		// 更新企业管理
		update: baseApi.update,
		// 删除企业管理
		delete: baseApi.delete,
		// 批量删除企业管理
		batchDelete: baseApi.batchDelete,
		// 导出企业管理数据
		exportData: baseApi.exportData,
		// 导入企业管理数据
		importData: baseApi.importData,
		// 下载企业管理数据导入模板
		downloadTemplate: baseApi.downloadTemplate,


		// 查询企业选项列表
		selectList: function (cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "selectList",
                method: 'post',
            }, cancel);
        },
	}
}

// 企业管理实体
export interface RecordCompany {
	// 主键Id
	id: number;
	// 企业名称
	name?: string;
	// 企业性质
	nature: string;
	// 企业联系方式
	tel: string;
	// 企业联系人
	linkman: string;
	// 权重
	weigh: number;
	// 工作地点行政区划
	companyCityCode: string;
	// 工作单位经济类型
	companyEconomicType: string;
	// 工作单位机构类型
	companyType: string;
	// 工作单位所属行业
	companyIndustry: string;
	// 查档码
	recordViewCode: string;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}