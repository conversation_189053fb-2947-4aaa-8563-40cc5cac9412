import { MenuTypeEnum } from '../models/menu-type-enum';
import { StatusEnum } from '../models/status-enum';
import { RecordChannelListTypeEnum } from './recordChannelListTypeEnum';

 /**
 * 档案目录查询输出参数
 *
 * @export
 * @interface ExportRecordCatalogOutput
 */
export interface ExportRecordCatalogOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof ExportRecordCatalogOutput
     */
    id: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof ExportRecordCatalogOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof ExportRecordCatalogOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof ExportRecordCatalogOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof ExportRecordCatalogOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof ExportRecordCatalogOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof ExportRecordCatalogOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof ExportRecordCatalogOutput
     */
    isDelete?: boolean;

    /**
     * 机构Id
     *
     * @type {number}
     * @memberof ExportRecordCatalogOutput
     */
    orgId?: number | null;

    /**
     * 父Id
     *
     * @type {number}
     * @memberof ExportRecordCatalogOutput
     */
    fatherId?: number | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof ExportRecordCatalogOutput
     */
    name?: string | null;

    /**
     * 描述
     *
     * @type {string}
     * @memberof ExportRecordCatalogOutput
     */
    description?: string | null;

    /**
     * 子类
     *
     * @type {Array<ExportRecordCatalogOutput>}
     * @memberof ExportRecordCatalogOutput
     */
    children?: Array<ExportRecordCatalogOutput> | null;

    /**
     * 权重
     *
     * @type {number}
     * @memberof ExportRecordCatalogOutput
     */
    weigh?:  number | null;
}