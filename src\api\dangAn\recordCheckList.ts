﻿import {useBaseApi} from '/@/api/base';

// 档案清单与补充材料接口服务
export const useRecordCheckListApi = () => {
	const baseApi = useBaseApi("recordCheckList");
	return {
		// 分页查询档案清单与补充材料
		page: baseApi.page,
		// 查看档案清单与补充材料详细
		detail: baseApi.detail,
		// 新增档案清单与补充材料
		add: baseApi.add,
		// 更新档案清单与补充材料
		update: baseApi.update,
		// 删除档案清单与补充材料
		delete: baseApi.delete,
		// 批量删除档案清单与补充材料
		batchDelete: baseApi.batchDelete,
		// 导出档案清单与补充材料数据
		exportData: baseApi.exportData,
		// 导入档案清单与补充材料数据
		importData: baseApi.importData,
		// 下载档案清单与补充材料数据导入模板
		downloadTemplate: baseApi.downloadTemplate,


		// 查询档案清单与补充材料
		query: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "query",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 档案清单与补充材料实体
export interface RecordCheckList {
	// 主键Id
	id: number;
	// 档案号
	recordId?: number;
	// 档案目录Id
	catalogId?: number;
	// 材料名称
	fileName?: string;
	// 形成时间
	fileTime?: string;
	// 单位
	fileUnit?: string;
	// 数量
	fileNumber?: number;
	// 页数
	filePages: number;
	// 封面页数
	fileCoverPages: number;
	// 是否原件 true=是,false=否
	fileIsCopyData?: boolean;
	// 是否补充 true=是,false=否
	fileIsSupplementData?: boolean;
	// 补充材料类型
	fileSupplementType: string;
	// 备注
	fileContent: string;
	// 数字化标识 true=是,false=否
	fileIsDigitization?: boolean;
	// 录入时间
	fileAddTime: string;
	// 录入人Id
	adminId: number;
	// 权重
	weigh?: number;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}