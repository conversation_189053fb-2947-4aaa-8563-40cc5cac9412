import { MenuTypeEnum } from '../models/menu-type-enum';
import { StatusEnum } from '../models/status-enum';
import { RecordChannelListTypeEnum } from './recordChannelListTypeEnum';

 /**
 * 档案柜查询输出参数
 *
 * @export
 * @interface QueryRecordChanneOutput
 */
export interface QueryRecordChanneOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof QueryRecordChanneOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof QueryRecordChanneOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof QueryRecordChanneOutput
     */
    isDelete?: boolean;

    /**
     * 模型Id
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    modelId?: number | null;

    /**
     * 父Id
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    parentId?: number | null;

    /**
     * 名称
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    name?: string | null;

    /**
     * 图片
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    image?: string | null;

    /**
     * 标志
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    flag?: string | null;

    /**
     * SEO标题
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    seotitle?: string | null;

    /**
     * 关键字
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    keyWords?: string | null;

    /**
     * 描述
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    description?: string | null;

    /**
     * 自定义名称
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    diyName?: string | null;

    /**
     * 外部链接
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    outLink?: string | null;

    /**
     * 链接类型
     *
     * @type {string}
     * @memberof QueryRecordChanneOutput
     */
    linkType?: string | null;

    /**
     * 实际档案数量
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    items?: number | null;

    /**
     * 列表数据类型
     *
     * @type {RecordChannelListTypeEnum}
     * @memberof QueryRecordChanneOutput
     */
    listType?: RecordChannelListTypeEnum;

    /**
     * 是否已满 true:已满 false:未满
     *
     * @type {boolean}
     * @memberof QueryRecordChanneOutput
     */
    isContribute?: boolean;

    /**
     * 是否导航显示 true:是 false:否
     *
     * @type {boolean}
     * @memberof QueryRecordChanneOutput
     */
    isNav?: boolean | null;

    /**
     * 状态
     *
     * @type {StatusEnum}
     * @memberof QueryRecordChanneOutput
     */
    status?: StatusEnum;

    /**
     * 库房Id
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    cabinetRecordRoomId?: number | null;

    /**
     * 可存放档案数量
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    cabinetMax?: number | null;

    /**
     * 机构Id
     *
     * @type {number}
     * @memberof QueryRecordChanneOutput
     */
    orgId?: number | null;

    /**
     * 子类
     *
     * @type {Array<QueryRecordChanneOutput>}
     * @memberof QueryRecordChanneOutput
     */
    children?: Array<QueryRecordChanneOutput> | null;
}