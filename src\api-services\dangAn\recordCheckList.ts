import { RefordFileEnum } from "./refordFileEnum";

/**
 * 档案清单与补充材料查询输出参数
 *
 * @export
 * @interface QueryRecordCheckListOutput
 */
export interface QueryRecordCheckListOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof QueryRecordCheckListOutput
     */
    id: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof QueryRecordCheckListOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof QueryRecordCheckListOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof QueryRecordCheckListOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof QueryRecordCheckListOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof QueryRecordCheckListOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof QueryRecordCheckListOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof QueryRecordCheckListOutput
     */
    isDelete?: boolean;

    /**
     * 机构Id
     *
     * @type {number}
     * @memberof QueryRecordCheckListOutput
     */
    orgId?: number | null;

    /**
     * 父Id
     *
     * @type {number}
     * @memberof QueryRecordCheckListOutput
     */
    fatherId?: number | null;

    /**
     * 子集
     *
     * @type {Array<QueryRecordCheckListOutput>}
     * @memberof QueryRecordCheckListOutput
     */
    children?: Array<QueryRecordCheckListOutput> | null;

    /**
     * 类型
     *
     * @type {RefordFileEnum}
     * @memberof QueryRecordCheckListOutput
     */
    type: RefordFileEnum;










    /**
     * 档案号
     *
     * @type {number}
     * @memberof QueryRecordCheckListOutput
     */
    recordId: number;

    /**
     * 材料名称
     *
     * @type {string}
     * @memberof QueryRecordCheckListOutput
     */
    name: string;

    /**
     * 形成时间
     *
     * @type {Date}
     * @memberof QueryRecordCheckListOutput
     */
    fileTime: Date;

    /**
     * 单位
     *
     * @type {string}
     * @memberof QueryRecordCheckListOutput
     */
    fileUnit: string;

    /**
     * 数量
     *
     * @type {number}
     * @memberof QueryRecordCheckListOutput
     */
    fileNumber: number;

    /**
     * 页数
     *
     * @type {number | null}
     * @memberof QueryRecordCheckListOutput
     */
    filePages?: number | null;

    /**
     * 封面页数
     *
     * @type {number | null}
     * @memberof QueryRecordCheckListOutput
     */
    fileCoverPages?: number | null;

    /**
     * 是否原件
     *
     * @type {boolean}
     * @memberof QueryRecordCheckListOutput
     */
    fileIsCopyData: boolean;

    /**
     * 是否补充
     *
     * @type {boolean}
     * @memberof QueryRecordCheckListOutput
     */
    fileIsSupplementData: boolean;

    /**
     * 补充材料类型
     *
     * @type {string | null}
     * @memberof QueryRecordCheckListOutput
     */
    fileSupplementType?: string | null;

    /**
     * 备注
     *
     * @type {string | null}
     * @memberof QueryRecordCheckListOutput
     */
    fileContent?: string | null;

    /**
     * 数字化标识
     *
     * @type {boolean}
     * @memberof QueryRecordCheckListOutput
     */
    fileIsDigitization: boolean;

    /**
     * 录入人姓名
     *
     * @type {string}
     * @memberof QueryRecordCheckListOutput
     */
    adminName: string;

    /**
     * 权重
     *
     * @type {number}
     * @memberof QueryRecordCheckListOutput
     */
    weigh: number;
}