﻿import {useBaseApi} from '/@/api/base';

// 档案出库入库记录接口服务
export const useRecordEntryandoutApi = () => {
	const baseApi = useBaseApi("recordEntryandout");
	return {
		// 分页查询档案出库入库记录
		page: baseApi.page,
		// 查看档案出库入库记录详细
		detail: baseApi.detail,
		// 新增档案出库入库记录
		add: baseApi.add,
		// 更新档案出库入库记录
		update: baseApi.update,
		// 删除档案出库入库记录
		delete: baseApi.delete,
		// 批量删除档案出库入库记录
		batchDelete: baseApi.batchDelete,
		// 导出档案出库入库记录数据
		exportData: baseApi.exportData,
		// 导入档案出库入库记录数据
		importData: baseApi.importData,
		// 下载档案出库入库记录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,


		// 获取最新记录
		first: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "first",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 档案出库入库记录实体
export interface RecordEntryandout {
	// 主键Id
	id: number;
	// 档案号
	recordId?: number;
	// 出入库类型
	logType?: number;
	// 出入库备注
	mark: string;
	// 所在档案柜Id
	channelId?: number;
	// 修改者Id
	recordUpdateUserId?: number;
	// 修改者姓名
	recordUpdateUserName: string;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}