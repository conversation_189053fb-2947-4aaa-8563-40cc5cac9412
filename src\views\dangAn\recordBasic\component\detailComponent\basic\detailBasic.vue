<script lang="ts" setup name="recordBasic">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordBasicApi } from '/@/api/dangAn/recordBasic';
import editDialog from './editDialog.vue';

const recordBasicApi = useRecordBasicApi();
const editDialogRef = ref();

//父级传递来的数据
const props = defineProps({
	recordId: {
		// 档案Id
		type: Number,
		required: true,
		default: null,
	},
});

const state = reactive({
	exportLoading: false,
	tableLoading: false,

	detailData: {} as any, // 详细数据
});

// 页面加载时
onMounted(async () => {
	await handleQuery();
});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;

	if (props.recordId != null) {
		state.detailData = props.recordId ? await recordBasicApi.detail(props.recordId).then((res) => res.data.result) : {};
	}

	state.tableLoading = false;
};
</script>
<template>
	<div>
		<el-card shadow="hover" style="margin-top: 5px">
			<template #header>
				<div class="card-header">
					<span>档案号：{{ state.detailData.id }}</span>
				</div>
			</template>
			<el-descriptions :column="5" border size="small" label-width="120px">
				<el-descriptions-item label="姓名">{{ state.detailData.name }}</el-descriptions-item>
				<el-descriptions-item label="曾用名">{{ state.detailData.formerName }}</el-descriptions-item>
				<el-descriptions-item label="证件类型">
					<g-sys-dict v-model="state.detailData.identificationType" code="IdentificationTypeEnum" />
				</el-descriptions-item>
				<el-descriptions-item label="证件号码">{{ state.detailData.identificationNumber }}</el-descriptions-item>
				<el-descriptions-item label="出生日期">{{ state.detailData.birthday }}</el-descriptions-item>
				<el-descriptions-item label="性别">
					<g-sys-dict v-model="state.detailData.gender" code="GenderEnum" />
				</el-descriptions-item>
				<el-descriptions-item label="民族">
					<g-sys-dict v-model="state.detailData.nationality" code="NationalityEnum" />
				</el-descriptions-item>
				<el-descriptions-item label="委托原因">
					<g-sys-dict v-model="state.detailData.entrustReason" code="Entrust_reason" />
				</el-descriptions-item>
				<el-descriptions-item label="出生地">{{ state.detailData.birthplace }}</el-descriptions-item>
				<el-descriptions-item label="籍贯">{{ state.detailData.censusRegister }}</el-descriptions-item>
				<el-descriptions-item label="户口编号">{{ state.detailData.registerNumber }}</el-descriptions-item>
				<el-descriptions-item label="婚姻状况">
					<g-sys-dict v-model="state.detailData.maritalStatus" code="MaritalStatusEnum" />
				</el-descriptions-item>
				<el-descriptions-item label="政治面貌">
					<g-sys-dict v-model="state.detailData.politicsStatus" code="PoliticsStatusEnum" />
				</el-descriptions-item>
				<el-descriptions-item label="分支机构">{{ state.detailData.reasonForProxy }}</el-descriptions-item>
				<el-descriptions-item label="个人身份">
					<g-sys-dict v-model="state.detailData.userType" code="User_type" />
				</el-descriptions-item>
				<el-descriptions-item label="委托方式">
					<g-sys-dict v-model="state.detailData.proxyType" code="Proxy_type" />
				</el-descriptions-item>
				<el-descriptions-item label="原档案号">{{ state.detailData.recordNumberOld }}</el-descriptions-item>
				<el-descriptions-item label="家庭地址">{{ state.detailData.homeAddress }}</el-descriptions-item>
				<el-descriptions-item label="手机">{{ state.detailData.mobile }}</el-descriptions-item>
				<el-descriptions-item label="办公电话">{{ state.detailData.officeTel }}</el-descriptions-item>
				<el-descriptions-item label="家庭电话">{{ state.detailData.homeTel }}</el-descriptions-item>
				<el-descriptions-item label="其他号码">{{ state.detailData.otherContacts }}</el-descriptions-item>
				<el-descriptions-item label="电子邮箱">{{ state.detailData.email }}</el-descriptions-item>
				<el-descriptions-item label="家庭邮编">{{ state.detailData.homeCode }}</el-descriptions-item>
				<el-descriptions-item label="紧急联系人">{{ state.detailData.emergencyContact }}</el-descriptions-item>
				<el-descriptions-item label="联系人与本人关系">{{ state.detailData.relationship }}</el-descriptions-item>
				<el-descriptions-item label="联系人手机号">{{ state.detailData.contactMobile }}</el-descriptions-item>
				<el-descriptions-item label="联系人电话">{{ state.detailData.contactTel }}</el-descriptions-item>
				<el-descriptions-item label="邮政编码">{{ state.detailData.postalCode }}</el-descriptions-item>
				<el-descriptions-item label="通信地址">{{ state.detailData.communicationAddress }}</el-descriptions-item>
				<el-descriptions-item label="系统识别码">{{ state.detailData.cystemIdentificationCode }}</el-descriptions-item>
				<el-descriptions-item label="参加工作时间">{{ state.detailData.worktime }}</el-descriptions-item>
				<el-descriptions-item label="户籍区划代码">{{ state.detailData.birthplaceCityCode }}</el-descriptions-item>
				<el-descriptions-item label="健康状况">
					<g-sys-dict v-model="state.detailData.healthStatus" code="Health_status" />
				</el-descriptions-item>
				<el-descriptions-item label="语种">{{ state.detailData.language }}</el-descriptions-item>
				<el-descriptions-item label="语种熟练度">
					<g-sys-dict v-model="state.detailData.languageProficient" code="LanguageProficientEnum" />
				</el-descriptions-item>
				<el-descriptions-item label="宗教信仰">{{ state.detailData.belief }}</el-descriptions-item>
				<el-descriptions-item label="工作职位(岗位)类型">{{ state.detailData.companyPositionType }}</el-descriptions-item>
				<el-descriptions-item label="工作单位">{{ state.detailData.companyName }}</el-descriptions-item>
				<el-descriptions-item label="户口所在地址">{{ state.detailData.rigisterAddress }}</el-descriptions-item>
				<el-descriptions-item label="档案状态">
					<g-sys-dict v-model="state.detailData.recordStatus" code="RecordStatusEnum" />
				</el-descriptions-item>
				<el-descriptions-item label="物理地址">{{ state.detailData.physicalAddress }}</el-descriptions-item>
				<el-descriptions-item label="所在库">{{ state.detailData.channelOneId }}</el-descriptions-item>
				<el-descriptions-item label="所在柜">{{ state.detailData.channelTwoId }}</el-descriptions-item>
				<el-descriptions-item label="所在层">{{ state.detailData.channelThreeId }}</el-descriptions-item>
				<el-descriptions-item label="毕业时间">{{ state.detailData.graduateTime }}</el-descriptions-item>
				<el-descriptions-item label="毕业学校">{{ state.detailData.graduateSchool }}</el-descriptions-item>
				<el-descriptions-item label="所学专业">{{ state.detailData.graduateMajor }}</el-descriptions-item>
				<el-descriptions-item label="学历">
					<g-sys-dict v-model="state.detailData.personEdu" code="Person_edu" />
				</el-descriptions-item>
				<el-descriptions-item label="学历性质">{{ state.detailData.eduNature }}</el-descriptions-item>
				<el-descriptions-item label="入党团时间">{{ state.detailData.politicsTime }}</el-descriptions-item>
				<el-descriptions-item label="录入人姓名">{{ state.detailData.adminName }}</el-descriptions-item>
			</el-descriptions>

			<el-button icon="ele-Edit" style="margin-top: 10px" size="small" type="primary" @click="editDialogRef.openDialog(state.detailData, '编辑档案基本信息')" v-auth="'recordBasic:update'">
				编辑
			</el-button>
		</el-card>

		<editDialog ref="editDialogRef" @reloadTable="handleQuery()" />
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}

.linkedList :deep(.el-radio-button__inner) {
	width: 100%;
}
</style>
