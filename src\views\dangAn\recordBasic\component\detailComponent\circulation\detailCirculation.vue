<script lang="ts" setup name="recordBasic">
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import rollinDialog from './component/rollin/rollinDialog.vue';
import entryandoutDialog from './component/entryandout/entryandoutDialog.vue';
import rolloutDialog from './component/rollout/rolloutDialog.vue';
import { QueryRecordChanneOutput } from '/@/api-services/dangAn/recordChannel';

//父级传递来的数据
const props = defineProps({
	recordId: {
		// 档案Id
		type: Number,
		required: true,
		default: null,
	},
	channelData: {
		// 档案柜数据
		type: Array<QueryRecordChanneOutput>,
		required: true,
		default: [],
	},
});

const state = reactive({
	exportLoading: false,
});

// 页面加载时
onMounted(async () => {});
</script>
<template>
	<div class="recordBasic-container" v-loading="state.exportLoading">
		<el-tabs type="border-card">
			<el-tab-pane label="转入信息">
				<rollinDialog :recordId="+props.recordId" />
			</el-tab-pane>

			<el-tab-pane label="保管信息">
				<entryandoutDialog :recordId="+props.recordId" :channelData="props.channelData" />
			</el-tab-pane>

			<el-tab-pane label="转出信息">
				<rolloutDialog :recordId="+props.recordId" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
