﻿import {useBaseApi} from '/@/api/base';

// 档案转出详细记录接口服务
export const useRecordRolloutApi = () => {
	const baseApi = useBaseApi("recordRollout");
	return {
		// 分页查询档案转出详细记录
		page: baseApi.page,
		// 查看档案转出详细记录详细
		detail: baseApi.detail,
		// 新增档案转出详细记录
		add: baseApi.add,
		// 更新档案转出详细记录
		update: baseApi.update,
		// 删除档案转出详细记录
		delete: baseApi.delete,
		// 批量删除档案转出详细记录
		batchDelete: baseApi.batchDelete,
		// 导出档案转出详细记录数据
		exportData: baseApi.exportData,
		// 导入档案转出详细记录数据
		importData: baseApi.importData,
		// 下载档案转出详细记录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,


		// 树状查询档案柜
		queryRecordId: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + "queryRecordId",
                method: 'post',
                data,
            }, cancel);
        },
	}
}

// 档案转出详细记录实体
export interface RecordRollout {
	// 主键Id
	id: number;
	// 档案号
	recordId?: number;
	// 转出申请时间
	applyTime: string;
	// 转出状态
	status: string;
	// 转出原因
	reason: string;
	// 转至机构名称
	organization: string;
	// 单位性质
	companyNature: string;
	// 转出地址
	address: string;
	// 邮编
	postalCode: number;
	// 档案接收人
	recipient: string;
	// 电话
	recipientTel: string;
	// 转出提醒时间
	tipTime: string;
	// 转出表编号
	serialNumber: number;
	// 转出机要号
	confidentialNumber: string;
	// 档案转出时间
	outTime: string;
	// 人事关系转出
	humanAgencey: string;
	// 人事关系转出时间
	humanAgenceyTime: string;
	// 行政介绍信
	introductionLetter: string;
	// 行政介绍信出具人
	introductionLetterPerson: string;
	// 行政介绍信开出日期
	introductionLetterAddtime: string;
	// 工资介绍信
	payrollLetter: string;
	// 工资介绍信出具人
	payrollLetterPerson: string;
	// 转出人员
	personOut: string;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}