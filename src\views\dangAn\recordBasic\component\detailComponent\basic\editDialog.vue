﻿<script lang="ts" name="recordBasic" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { User, CreditCard, Phone, UserFilled, OfficeBuilding, Reading, More, Document } from '@element-plus/icons-vue';
import { useRecordBasicApi } from '/@/api/dangAn/recordBasic';
import { useRecordCompanyApi } from '/@/api/dangAn/recordCompany';
import { getBirthDateFromIdCard, getGenderFromIdCard } from '/@/utils/myUtils/myUtils';

//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);
const recordBasicApi = useRecordBasicApi();
const recordCompanyApi = useRecordCompanyApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	companyRadio: [] as Array<any>, // 企业选项列表
});

// 折叠面板激活状态
const activeCollapse = ref(['basic', 'identity', 'contact']);

// 自行添加其他规则
const rules = ref<FormRules>({
	name: [{ required: true, message: '请选择姓名！', trigger: 'blur' }],
	identificationType: [{ required: true, message: '请选择证件类型！', trigger: 'change' }],
	identificationNumber: [{ required: true, message: '请选择证件号码！', trigger: 'blur' }],
	birthday: [{ required: true, message: '请选择出生日期！', trigger: 'change' }],
	gender: [{ required: true, message: '请选择性别！', trigger: 'change' }],
	nationality: [{ required: true, message: '请选择民族！', trigger: 'change' }],
	politicsStatus: [{ required: true, message: '请选择政治面貌！', trigger: 'change' }],
	proxyType: [{ required: true, message: '请选择委托方式！', trigger: 'change' }],
	mobile: [{ required: true, message: '请选择手机！', trigger: 'blur' }],
	birthplaceCityCode: [{ required: true, message: '请输入户籍区划代码！', trigger: 'blur' }],
	personEdu: [{ required: true, message: '请选择学历！', trigger: 'change' }],
	companyId: [{ required: true, message: '请选择工作单位！', trigger: 'change' }],
});

// 页面加载时
onMounted(async () => {});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {};
	state.ruleForm = row.id ? await recordBasicApi.detail(row.id).then((res) => res.data.result) : JSON.parse(JSON.stringify(row));
	state.companyRadio = await recordCompanyApi.selectList().then((res) => res.data.result);
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit('reloadTable');
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;

			for (let i = 0; i < state.companyRadio.length; i++) {
				let item = state.companyRadio[i];

				if (item.id == values.companyId) {
					values.companyName = item.name;
					break;
				}
			}

			await recordBasicApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};

// 输入证件号处理
const inputIdentificationNumber = () => {
	if (state.ruleForm.identificationNumber != null && state.ruleForm.identificationNumber.length == 18) {
		try {
			state.ruleForm.birthday = getBirthDateFromIdCard(state.ruleForm.identificationNumber); // 生日
			state.ruleForm.gender = getGenderFromIdCard(state.ruleForm.identificationNumber); // 性别
		} catch (error) {
			console.log(error);
		}
	}
};

// 日期限制
const disabledDate = (time: Date) => {
	return time.getTime() > Date.now();
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordBasic-container">
		<el-dialog v-model="state.showDialog" :width="1500" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-form-item v-show="false">
					<el-input v-model="state.ruleForm.id" />
				</el-form-item>

				<el-collapse v-model="activeCollapse" class="form-collapse">
					<!-- 基本信息 -->
					<el-collapse-item title="基本信息" name="basic">
						<template #title>
							<div class="collapse-title">
								<el-icon><User /></el-icon>
								<span>基本信息</span>
							</div>
						</template>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="姓名" prop="name">
									<el-input v-model="state.ruleForm.name" placeholder="请输入姓名" maxlength="50" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="曾用名" prop="formerName">
									<el-input v-model="state.ruleForm.formerName" placeholder="请输入曾用名" maxlength="50" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="证件类型" prop="identificationType">
									<g-sys-dict
										v-model="state.ruleForm.identificationType"
										code="IdentificationTypeEnum"
										render-as="select"
										placeholder="请选择证件类型"
										@input="inputIdentificationNumber()"
										clearable
										filterable
									/>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="证件号码" prop="identificationNumber">
									<el-input v-model="state.ruleForm.identificationNumber" placeholder="请输入证件号码" maxlength="20" @input="inputIdentificationNumber()" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="出生日期" prop="birthday">
									<el-date-picker v-model="state.ruleForm.birthday" type="date" placeholder="出生日期" :disabled-date="disabledDate" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="性别" prop="gender">
									<g-sys-dict v-model="state.ruleForm.gender" code="GenderEnum" render-as="select" placeholder="请选择性别" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="出生地" prop="birthplace">
									<el-input v-model="state.ruleForm.birthplace" placeholder="请输入出生地" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="籍贯" prop="censusRegister">
									<el-input v-model="state.ruleForm.censusRegister" placeholder="请输入籍贯" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-collapse-item>

					<!-- 身份信息 -->
					<el-collapse-item title="身份信息" name="identity">
						<template #title>
							<div class="collapse-title">
								<el-icon><CreditCard /></el-icon>
								<span>身份信息</span>
							</div>
						</template>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="民族" prop="nationality">
									<g-sys-dict v-model="state.ruleForm.nationality" code="NationalityEnum" render-as="select" placeholder="请选择民族" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="政治面貌" prop="politicsStatus">
									<g-sys-dict v-model="state.ruleForm.politicsStatus" code="PoliticsStatusEnum" render-as="select" placeholder="请选择政治面貌" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="入党团时间" prop="politicsTime">
									<el-date-picker v-model="state.ruleForm.politicsTime" type="date" placeholder="入党团时间" :disabled-date="disabledDate" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="婚姻状况" prop="maritalStatus">
									<g-sys-dict v-model="state.ruleForm.maritalStatus" code="MaritalStatusEnum" render-as="select" placeholder="请选择婚姻状况" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="个人身份" prop="userType">
									<g-sys-dict v-model="state.ruleForm.userType" code="User_type" render-as="select" placeholder="请选择个人身份" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="户口编号" prop="registerNumber">
									<el-input v-model="state.ruleForm.registerNumber" placeholder="请输入户口编号" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="户籍区划代码" prop="birthplaceCityCode">
									<el-input-number v-model="state.ruleForm.birthplaceCityCode" placeholder="请输入户籍区划代码" clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="户口所在地址" prop="rigisterAddress">
									<el-input v-model="state.ruleForm.rigisterAddress" placeholder="请输入户口所在地址" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-collapse-item>

					<!-- 联系方式 -->
					<el-collapse-item title="联系方式" name="contact">
						<template #title>
							<div class="collapse-title">
								<el-icon><Phone /></el-icon>
								<span>联系方式</span>
							</div>
						</template>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="手机" prop="mobile">
									<el-input v-model="state.ruleForm.mobile" placeholder="请输入手机" maxlength="11" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="办公电话" prop="officeTel">
									<el-input v-model="state.ruleForm.officeTel" placeholder="请输入办公电话" maxlength="30" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="家庭电话" prop="homeTel">
									<el-input v-model="state.ruleForm.homeTel" placeholder="请输入家庭电话" maxlength="30" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="其他号码" prop="otherContacts">
									<el-input v-model="state.ruleForm.otherContacts" placeholder="请输入其他号码" maxlength="30" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="电子邮箱" prop="email">
									<el-input v-model="state.ruleForm.email" placeholder="请输入电子邮箱" maxlength="30" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="家庭地址" prop="homeAddress">
									<el-input v-model="state.ruleForm.homeAddress" placeholder="请输入家庭地址" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="家庭邮编" prop="homeCode">
									<el-input v-model="state.ruleForm.homeCode" placeholder="请输入家庭邮编" maxlength="10" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="通信地址" prop="communicationAddress">
									<el-input v-model="state.ruleForm.communicationAddress" placeholder="请输入通信地址" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="邮政编码" prop="postalCode">
									<el-input v-model="state.ruleForm.postalCode" placeholder="请输入邮政编码" maxlength="10" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="物理地址" prop="physicalAddress">
									<el-input v-model="state.ruleForm.physicalAddress" placeholder="请输入物理地址" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-collapse-item>

					<!-- 紧急联系人 -->
					<el-collapse-item title="紧急联系人" name="emergency">
						<template #title>
							<div class="collapse-title">
								<el-icon><UserFilled /></el-icon>
								<span>紧急联系人</span>
							</div>
						</template>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="紧急联系人" prop="emergencyContact">
									<el-input v-model="state.ruleForm.emergencyContact" placeholder="请输入紧急联系人" maxlength="10" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="联系人与本人关系" prop="relationship">
									<el-input v-model="state.ruleForm.relationship" placeholder="请输入与本人关系" maxlength="10" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="联系人手机号" prop="contactMobile">
									<el-input v-model="state.ruleForm.contactMobile" placeholder="请输入联系人手机号" maxlength="11" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="联系人电话" prop="contactTel">
									<el-input v-model="state.ruleForm.contactTel" placeholder="请输入联系人电话" maxlength="30" show-word-limit clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-collapse-item>

					<!-- 工作信息 -->
					<el-collapse-item title="工作信息" name="work">
						<template #title>
							<div class="collapse-title">
								<el-icon><OfficeBuilding /></el-icon>
								<span>工作信息</span>
							</div>
						</template>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="工作单位" prop="companyId">
									<el-select v-model="state.ruleForm.companyId" placeholder="请选择工作单位">
										<el-option v-for="item in state.companyRadio" :key="item.id" :label="item.name" :value="item.id" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="工作职位(岗位)类型" prop="companyPositionType">
									<el-input-number v-model="state.ruleForm.companyPositionType" placeholder="请输入工作职位(岗位)类型" clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="参加工作时间" prop="worktime">
									<el-date-picker v-model="state.ruleForm.worktime" placeholder="请输入参加工作时间" type="date" clearable :disabled-date="disabledDate" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="分支机构" prop="reasonForProxy">
									<el-input-number v-model="state.ruleForm.reasonForProxy" placeholder="请输入分支机构" clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-collapse-item>

					<!-- 教育信息 -->
					<el-collapse-item title="教育信息" name="education">
						<template #title>
							<div class="collapse-title">
								<el-icon><Reading /></el-icon>
								<span>教育信息</span>
							</div>
						</template>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="学历" prop="personEdu">
									<g-sys-dict v-model="state.ruleForm.personEdu" code="Person_edu" render-as="select" placeholder="请选择学历" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="学历性质" prop="eduNature">
									<el-input v-model="state.ruleForm.eduNature" placeholder="请输入学历性质" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="毕业时间" prop="graduateTime">
									<el-date-picker v-model="state.ruleForm.graduateTime" type="date" placeholder="毕业时间" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="毕业学校" prop="graduateSchool">
									<el-input v-model="state.ruleForm.graduateSchool" placeholder="请输入毕业学校" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="所学专业" prop="graduateMajor">
									<el-input v-model="state.ruleForm.graduateMajor" placeholder="请输入所学专业" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-collapse-item>

					<!-- 其他信息 -->
					<el-collapse-item title="其他信息" name="other">
						<template #title>
							<div class="collapse-title">
								<el-icon><More /></el-icon>
								<span>其他信息</span>
							</div>
						</template>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="健康状况" prop="healthStatus">
									<g-sys-dict v-model="state.ruleForm.healthStatus" code="Health_status" render-as="select" placeholder="请选择健康状况" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="语种" prop="language">
									<el-input v-model="state.ruleForm.language" placeholder="请输入语种" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="语种熟练度" prop="languageProficient">
									<g-sys-dict v-model="state.ruleForm.languageProficient" code="LanguageProficientEnum" render-as="select" placeholder="请选择语种熟练度" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="宗教信仰" prop="belief">
									<el-input v-model="state.ruleForm.belief" placeholder="请输入宗教信仰" maxlength="255" type="textarea" autosize show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="系统识别码" prop="cystemIdentificationCode">
									<el-input v-model="state.ruleForm.cystemIdentificationCode" placeholder="请输入系统识别码" maxlength="10" show-word-limit clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-collapse-item>

					<!-- 档案管理 -->
					<el-collapse-item title="档案管理" name="archive">
						<template #title>
							<div class="collapse-title">
								<el-icon><Document /></el-icon>
								<span>档案管理</span>
							</div>
						</template>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="委托原因" prop="entrustReason">
									<g-sys-dict v-model="state.ruleForm.entrustReason" code="Entrust_reason" render-as="select" placeholder="请选择委托原因" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="委托方式" prop="proxyType">
									<g-sys-dict v-model="state.ruleForm.proxyType" code="Proxy_type" render-as="select" placeholder="请选择委托方式" clearable filterable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="原档案号" prop="recordNumberOld">
									<el-input v-model="state.ruleForm.recordNumberOld" type="textarea" autosize placeholder="请输入原档案号" maxlength="255" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" class="mb20">
								<el-form-item label="录入人姓名" prop="adminName">
									<el-input v-model="state.ruleForm.adminName" placeholder="请输入录入人姓名" maxlength="50" show-word-limit clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-collapse-item>
				</el-collapse>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => (state.showDialog = false)">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}

.form-collapse {
	:deep(.el-collapse-item__header) {
		background-color: #f5f7fa;
		border-radius: 4px;
		margin-bottom: 8px;
		padding: 0 16px;
		font-weight: 500;

		&:hover {
			background-color: #ecf5ff;
		}
	}

	:deep(.el-collapse-item__content) {
		padding: 16px 0;
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		margin-bottom: 16px;
		background-color: #fafafa;
	}

	.collapse-title {
		display: flex;
		align-items: center;
		gap: 8px;
		color: #409eff;

		.el-icon {
			font-size: 16px;
		}

		span {
			font-size: 14px;
			font-weight: 500;
		}
	}
}

:deep(.el-collapse-item__arrow) {
	color: #409eff;
}

:deep(.form-collapse .el-collapse-item__content) {
	padding-right: 16px;
}
</style>
