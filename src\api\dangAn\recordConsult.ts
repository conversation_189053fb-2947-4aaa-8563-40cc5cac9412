﻿import {useBaseApi} from '/@/api/base';

// 档案查阅记录接口服务
export const useRecordConsultApi = () => {
	const baseApi = useBaseApi("recordConsult");
	return {
		// 分页查询档案查阅记录
		page: baseApi.page,
		// 查看档案查阅记录详细
		detail: baseApi.detail,
		// 新增档案查阅记录
		add: baseApi.add,
		// 更新档案查阅记录
		update: baseApi.update,
		// 删除档案查阅记录
		delete: baseApi.delete,
		// 批量删除档案查阅记录
		batchDelete: baseApi.batchDelete,
		// 导出档案查阅记录数据
		exportData: baseApi.exportData,
		// 导入档案查阅记录数据
		importData: baseApi.importData,
		// 下载档案查阅记录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 档案查阅记录实体
export interface RecordConsult {
	// 主键Id
	id: number;
	// 档案号
	recordId?: number;
	// 申请日期
	applyTime: string;
	// 业务办理日期
	handleTime: string;
	// 业务经办人id
	adminId?: number;
	// 业务经办人
	adminName: string;
	// 业务类型
	consultCategory: number;
	// 申请人
	applyName: string;
	// 使用人
	useName: string;
	// 使用单位
	useCompany: string;
	// 内容
	consultContent: string;
	// 申请备注
	applyRemarks: string;
	// 查询结果
	applyResult: number;
	// 业务办结人
	completionPerson: string;
	// 业务办结日期
	completionTime: string;
	// 办结备注
	completionRemarks: string;
	// 机构Id
	orgId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}