﻿<script lang="ts" name="recordEntryandout" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRecordEntryandoutApi } from '/@/api/dangAn/recordEntryandout';
import { useRecordBasicApi } from '/@/api/dangAn/recordBasic';
import { QueryRecordChanneOutput } from '/@/api-services/dangAn/recordChannel';

// 级联选择器配置选项
const cascaderProps = { checkStrictly: true, emitPath: false, value: 'id', label: 'name' };

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const recordEntryandoutApi = useRecordEntryandoutApi();
const recordBasicApi = useRecordBasicApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
  	recordId: null as Number | null, // 档案id
	channelData: [] as Array<QueryRecordChanneOutput>, // 档案柜数据

});

// 自行添加其他规则
const rules = ref<FormRules>({
  recordId: [{required: true, message: '请选择档案号！', trigger: 'blur',},],
  logType: [{required: true, message: '请选择出入库类型！', trigger: 'change',},],
  channelId: [{required: true, message: '请选择所在档案柜Id！', trigger: 'blur',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (title: string, recordId: Number, channelData: Array<QueryRecordChanneOutput>) => {
	state.title = title;

	state.ruleForm = await recordEntryandoutApi.first({recordId: recordId}).then(res => res.data.result) ?? {};
	state.ruleForm.id = undefined;
	state.ruleForm.recordId = recordId;
	state.recordId = recordId;

	state.channelData = channelData ?? [];

	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			values.id = state.recordId;
			
			await recordBasicApi.setChannel(values);

			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="recordEntryandout-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="档案号" prop="recordId">
							<el-input v-model="state.ruleForm.recordId" placeholder="请输入档案号" maxlength="20" show-word-limit clearable disabled />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="出入库类型" prop="logType">
							<g-sys-dict v-model="state.ruleForm.logType" code="RecordLogTypeEnum" render-as="select" placeholder="请选择出入库类型" clearable filterable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="所在档案柜" prop="channelId">
							<el-cascader :options="state.channelData" :props="cascaderProps" placeholder="请选择所在档案柜" clearable filterable class="w100" v-model="state.ruleForm.channelId">
								<template #default="{ node, data }">
									<span>{{ data.name }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-cascader>
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="修改者姓名" prop="recordUpdateUserName">
							<el-input v-model="state.ruleForm.recordUpdateUserName" placeholder="请输入修改者姓名" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="出入库备注" prop="mark">
							<el-input v-model="state.ruleForm.mark" :rows="2" type="textarea" placeholder="请输入出入库备注" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>