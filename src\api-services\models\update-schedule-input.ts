/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { FinishStatusEnum } from './finish-status-enum';
 /**
 * 
 *
 * @export
 * @interface UpdateScheduleInput
 */
export interface UpdateScheduleInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof UpdateScheduleInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateScheduleInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateScheduleInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateScheduleInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateScheduleInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateScheduleInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateScheduleInput
     */
    updateUserName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateScheduleInput
     */
    tenantId?: number | null;

    /**
     * 用户Id
     *
     * @type {number}
     * @memberof UpdateScheduleInput
     */
    userId?: number;

    /**
     * 日程日期
     *
     * @type {Date}
     * @memberof UpdateScheduleInput
     */
    scheduleTime?: Date | null;

    /**
     * 开始时间
     *
     * @type {string}
     * @memberof UpdateScheduleInput
     */
    startTime?: string | null;

    /**
     * 结束时间
     *
     * @type {string}
     * @memberof UpdateScheduleInput
     */
    endTime?: string | null;

    /**
     * @type {FinishStatusEnum}
     * @memberof UpdateScheduleInput
     */
    status?: FinishStatusEnum;

    /**
     * 日程内容
     *
     * @type {string}
     * @memberof UpdateScheduleInput
     */
    content: string;
}
