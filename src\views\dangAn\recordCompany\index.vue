﻿<script lang="ts" setup name="recordCompany">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordCompanyApi } from '/@/api/dangAn/recordCompany';
import editDialog from '/@/views/dangAn/recordCompany/component/editDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from '/@/components/table/importData.vue';

const recordCompanyApi = useRecordCompanyApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
	exportLoading: false,
	tableLoading: false,
	stores: {},
	showAdvanceQueryUI: false,
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'createTime', // 默认的排序字段
		order: 'descending', // 排序方向
		descStr: 'descending', // 降序排序的关键字符
	},
	tableData: [],
});

// 页面加载时
onMounted(async () => {});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	const result = await recordCompanyApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
	state.tableParams.total = result?.total;
	state.tableData = result?.items ?? [];
	state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

// 删除
const delRecordCompany = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordCompanyApi.delete({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 批量删除
const batchDelRecordCompany = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordCompanyApi.batchDelete(state.selectData.map((u) => ({ id: u.id }))).then((res) => {
				ElMessage.success(`成功批量删除${res.data.result}条记录`);
				handleQuery();
			});
		})
		.catch(() => {});
};

// 导出数据
const exportRecordCompanyCommand = async (command: string) => {
	try {
		state.exportLoading = true;
		if (command === 'select') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams, { selectKeyList: state.selectData.map((u) => u.id) });
			await recordCompanyApi.exportData(params).then((res) => downloadStreamFile(res));
		} else if (command === 'current') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams);
			await recordCompanyApi.exportData(params).then((res) => downloadStreamFile(res));
		} else if (command === 'all') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams, { page: 1, pageSize: 99999999 });
			await recordCompanyApi.exportData(params).then((res) => downloadStreamFile(res));
		}
	} finally {
		state.exportLoading = false;
	}
};

handleQuery();
</script>
<template>
	<div class="recordCompany-container px-3 pt-3" v-loading="state.exportLoading">
		<div class="bg-white rounded-md pt-5 px-5 mb-4">
			<el-form :model="state.tableQueryParams" ref="queryForm" inline v-if="!state.showAdvanceQueryUI">
				<!-- 简单查询模式 -->
				<el-form-item label="关键字">
					<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
				</el-form-item>

				<el-form-item>
					<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordCompany:page'" v-reclick="1000"> 查询 </el-button>
					<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})" style="margin-left: 5px"> 重置 </el-button>
					<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" style="margin-left: 5px"> 高级查询 </el-button>
					<el-button type="danger" style="margin-left: 5px" icon="ele-Delete" @click="batchDelRecordCompany" :disabled="state.selectData.length == 0" v-auth="'recordCompany:batchDelete'">
						删除
					</el-button>
					<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef.openDialog(null, '新增企业管理')" v-auth="'recordCompany:add'"> 新增 </el-button>
					<el-dropdown :show-timeout="70" :hide-timeout="50" @command="exportRecordCompanyCommand">
						<el-button type="primary" style="margin-left: 5px" icon="ele-FolderOpened" v-reclick="20000" v-auth="'recordCompany:export'"> 导出 </el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item command="select" :disabled="state.selectData.length == 0">导出选中</el-dropdown-item>
								<el-dropdown-item command="current">导出本页</el-dropdown-item>
								<el-dropdown-item command="all">导出全部</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
					<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'recordCompany:import'"> 导入 </el-button>
				</el-form-item>
			</el-form>

			<el-form :model="state.tableQueryParams" ref="queryForm" :label-width="120" v-if="state.showAdvanceQueryUI">
				<!-- 高级查询模式 - 3列布局 -->
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="企业名称">
							<el-input v-model="state.tableQueryParams.name" clearable placeholder="请输入企业名称" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="企业性质">
							<g-sys-dict v-model="state.tableQueryParams.nature" code="Company_nature" render-as="select" placeholder="请选择企业性质" clearable filterable />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="企业联系方式">
							<el-input v-model="state.tableQueryParams.tel" clearable placeholder="请输入企业联系方式" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="企业联系人">
							<el-input v-model="state.tableQueryParams.linkman" clearable placeholder="请输入企业联系人" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="工作地点行政区划">
							<g-sys-dict v-model="state.tableQueryParams.companyCityCode" code="Company_cityCode" render-as="select" placeholder="请选择工作地点行政区划" clearable filterable />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="工作单位经济类型">
							<g-sys-dict v-model="state.tableQueryParams.companyEconomicType" code="Company_economicType" render-as="select" placeholder="请选择工作单位经济类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="工作单位机构类型">
							<g-sys-dict v-model="state.tableQueryParams.companyType" code="Company_type" render-as="select" placeholder="请选择工作单位机构类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="工作单位所属行业">
							<g-sys-dict v-model="state.tableQueryParams.companyIndustry" code="Company_industry" render-as="select" placeholder="请选择工作单位所属行业" clearable filterable />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="查档码">
							<el-input v-model="state.tableQueryParams.recordViewCode" clearable placeholder="请输入查档码" />
						</el-form-item>
					</el-col>
					<el-col :span="16">
						<el-form-item>
							<div class="flex items-center justify-end w-full">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordCompany:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})" style="margin-left: 5px"> 重置 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" style="margin-left: 5px"> 隐藏 </el-button>

								<el-button type="danger" icon="ele-Delete" @click="batchDelRecordCompany" :disabled="state.selectData.length == 0" v-auth="'recordCompany:batchDelete'"> 删除 </el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef.openDialog(null, '新增企业管理')" v-auth="'recordCompany:add'"> 新增 </el-button>
								<el-dropdown :show-timeout="70" :hide-timeout="50" @command="exportRecordCompanyCommand">
									<el-button type="primary" style="margin-left: 5px" icon="ele-FolderOpened" v-reclick="20000" v-auth="'recordCompany:export'"> 导出 </el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item command="select" :disabled="state.selectData.length == 0">导出选中</el-dropdown-item>
											<el-dropdown-item command="current">导出本页</el-dropdown-item>
											<el-dropdown-item command="all">导出全部</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
								<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'recordCompany:import'"> 导入 </el-button>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>

		<el-card class="full-table" shadow="hover">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" v-if="auth('recordCompany:batchDelete') || auth('recordCompany:export')" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="name" label="企业名称" show-overflow-tooltip />
				<el-table-column prop="nature" label="企业性质" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.nature" code="Company_nature" />
					</template>
				</el-table-column>
				<el-table-column prop="tel" label="企业联系方式" show-overflow-tooltip />
				<el-table-column prop="linkman" label="企业联系人" show-overflow-tooltip />
				<el-table-column prop="companyCityCode" label="工作地点行政区划" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.companyCityCode" code="Company_cityCode" />
					</template>
				</el-table-column>
				<el-table-column prop="companyEconomicType" label="工作单位经济类型" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.companyEconomicType" code="Company_economicType" />
					</template>
				</el-table-column>
				<el-table-column prop="companyType" label="工作单位机构类型" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.companyType" code="Company_type" />
					</template>
				</el-table-column>
				<el-table-column prop="companyIndustry" label="工作单位所属行业" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.companyIndustry" code="Company_industry" />
					</template>
				</el-table-column>
				<el-table-column prop="recordViewCode" label="查档码" show-overflow-tooltip />
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('recordCompany:update') || auth('recordCompany:delete')">
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="editDialogRef.openDialog(scope.row, '编辑企业管理')" v-auth="'recordCompany:update'"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="primary" @click="delRecordCompany(scope.row)" v-auth="'recordCompany:delete'"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
			<ImportData ref="importDataRef" :import="recordCompanyApi.importData" :download="recordCompanyApi.downloadTemplate" v-auth="'recordCompany:import'" @refresh="handleQuery" />
			<printDialog ref="printDialogRef" :title="'打印企业管理'" @reloadTable="handleQuery" />
			<editDialog ref="editDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
